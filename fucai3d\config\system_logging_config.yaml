# 福彩3D预测系统 - 系统级日志配置
# 支持P8融合系统和所有预测器的统一日志管理
# 创建日期: 2025-01-14

version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  
  detailed:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  
  performance:
    format: "%(asctime)s - PERF - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  
  alert:
    format: "%(asctime)s - ALERT - %(levelname)s - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
  
  # 系统主日志
  system_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/system.log
    maxBytes: 20971520  # 20MB
    backupCount: 10
    encoding: utf8
  
  # 系统错误日志
  system_error:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/system_error.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  # P8融合系统日志
  fusion_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/fusion_system.log
    maxBytes: 20971520  # 20MB
    backupCount: 10
    encoding: utf8
  
  # 性能监控日志
  performance_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: performance
    filename: logs/performance.log
    maxBytes: 10485760  # 10MB
    backupCount: 7
    encoding: utf8
  
  # 告警日志
  alert_file:
    class: logging.handlers.RotatingFileHandler
    level: WARNING
    formatter: alert
    filename: logs/alerts.log
    maxBytes: 5242880  # 5MB
    backupCount: 5
    encoding: utf8
  
  # 预测器日志
  predictor_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/predictors.log
    maxBytes: 15728640  # 15MB
    backupCount: 8
    encoding: utf8

loggers:
  # P8融合系统日志
  FusionPredictor:
    level: INFO
    handlers: [console, fusion_file, system_error]
    propagate: false
  
  ProbabilityFusionEngine:
    level: INFO
    handlers: [console, fusion_file, system_error]
    propagate: false
  
  ConstraintOptimizer:
    level: INFO
    handlers: [console, fusion_file, system_error]
    propagate: false
  
  IntelligentRanker:
    level: INFO
    handlers: [console, fusion_file, system_error]
    propagate: false
  
  DynamicWeightAdjuster:
    level: INFO
    handlers: [console, fusion_file, system_error]
    propagate: false
  
  PerformanceMonitor:
    level: INFO
    handlers: [console, performance_file, system_error]
    propagate: false
  
  ReportGenerator:
    level: INFO
    handlers: [console, fusion_file, system_error]
    propagate: false
  
  AutoAdjustmentTrigger:
    level: INFO
    handlers: [console, fusion_file, system_error]
    propagate: false
  
  # 预测器日志
  SumPredictor:
    level: INFO
    handlers: [console, predictor_file, system_error]
    propagate: false
  
  SpanPredictor:
    level: INFO
    handlers: [console, predictor_file, system_error]
    propagate: false
  
  HundredsPredictor:
    level: INFO
    handlers: [console, predictor_file, system_error]
    propagate: false
  
  TensPredictor:
    level: INFO
    handlers: [console, predictor_file, system_error]
    propagate: false
  
  UnitsPredictor:
    level: INFO
    handlers: [console, predictor_file, system_error]
    propagate: false
  
  # 数据访问层日志
  FusionDataAccess:
    level: INFO
    handlers: [console, system_file, system_error]
    propagate: false
  
  # 统一接口日志
  UnifiedPredictorInterface:
    level: INFO
    handlers: [console, system_file, system_error]
    propagate: false
  
  # 性能监控专用日志
  SystemPerformanceMonitor:
    level: INFO
    handlers: [performance_file, alert_file]
    propagate: false
  
  # 告警系统日志
  AlertSystem:
    level: WARNING
    handlers: [console, alert_file]
    propagate: false

# 根日志配置
root:
  level: WARNING
  handlers: [console, system_error]
