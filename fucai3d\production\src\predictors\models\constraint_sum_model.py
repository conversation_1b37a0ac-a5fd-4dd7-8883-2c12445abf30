#!/usr/bin/env python3
"""
约束优化和值模型（专属特征）

实现约束优化模型，与P3-P5位置预测器协同
实现约束一致性优化，确保和值预测与位置预测的一致性

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
import pickle
import logging
from typing import Dict, List, Tuple, Optional, Any
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import sqlite3
import json
from pathlib import Path
from scipy.optimize import minimize

class ConstraintSumModel:
    """约束优化和值模型（专属特征）"""
    
    def __init__(self, db_path: str, config: Optional[Dict] = None):
        """
        初始化约束优化和值模型
        
        Args:
            db_path: 数据库路径
            config: 模型配置参数
        """
        self.db_path = db_path
        self.model = None
        self.is_trained = False
        self.logger = logging.getLogger("ConstraintSumModel")
        
        # 约束优化参数
        self.constraint_params = {
            'position_weight': 0.7,    # 位置预测权重
            'sum_weight': 0.3,         # 和值预测权重
            'tolerance': 1.5,          # 约束容忍度
            'max_iterations': 100      # 最大迭代次数
        }
        
        # 更新配置参数
        if config and 'constraint' in config:
            self.constraint_params.update(config['constraint'])
        
        # 位置预测器接口（需要在运行时设置）
        self.position_predictors = {
            'hundreds': None,
            'tens': None,
            'units': None
        }
    
    def set_position_predictors(self, hundreds_predictor, tens_predictor, units_predictor):
        """
        设置位置预测器
        
        Args:
            hundreds_predictor: 百位预测器
            tens_predictor: 十位预测器
            units_predictor: 个位预测器
        """
        self.position_predictors['hundreds'] = hundreds_predictor
        self.position_predictors['tens'] = tens_predictor
        self.position_predictors['units'] = units_predictor
        
        self.logger.info("位置预测器设置完成")
    
    def get_position_predictions(self, X: np.ndarray) -> Dict[str, np.ndarray]:
        """
        获取位置预测结果
        
        Args:
            X: 特征矩阵
            
        Returns:
            位置预测结果字典
        """
        predictions = {}
        
        for position, predictor in self.position_predictors.items():
            if predictor is not None and hasattr(predictor, 'predict_proba'):
                try:
                    # 获取概率分布
                    proba = predictor.predict_proba(X)
                    predictions[f'{position}_prob'] = proba
                    
                    # 获取点预测
                    pred = predictor.predict(X)
                    predictions[f'{position}_pred'] = pred
                    
                except Exception as e:
                    self.logger.warning(f"获取{position}预测失败: {e}")
                    # 使用默认均匀分布
                    predictions[f'{position}_prob'] = np.ones((len(X), 10)) / 10
                    predictions[f'{position}_pred'] = np.full(len(X), 5)
            else:
                # 使用默认均匀分布
                predictions[f'{position}_prob'] = np.ones((len(X), 10)) / 10
                predictions[f'{position}_pred'] = np.full(len(X), 5)
        
        return predictions
    
    def calculate_expected_sum(self, position_predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """
        基于位置预测计算期望和值
        
        Args:
            position_predictions: 位置预测结果
            
        Returns:
            期望和值数组
        """
        hundreds_prob = position_predictions['hundreds_prob']
        tens_prob = position_predictions['tens_prob']
        units_prob = position_predictions['units_prob']
        
        expected_sums = []
        
        for i in range(len(hundreds_prob)):
            expected_sum = 0
            
            # 计算所有组合的期望和值
            for h in range(10):
                for t in range(10):
                    for u in range(10):
                        combination_sum = h + t + u
                        combination_prob = (hundreds_prob[i][h] * 
                                          tens_prob[i][t] * 
                                          units_prob[i][u])
                        expected_sum += combination_sum * combination_prob
            
            expected_sums.append(expected_sum)
        
        return np.array(expected_sums)
    
    def constraint_objective(self, sum_pred: float, expected_sum: float, 
                           position_predictions: Dict[str, float]) -> float:
        """
        约束优化目标函数
        
        Args:
            sum_pred: 和值预测
            expected_sum: 期望和值
            position_predictions: 位置预测
            
        Returns:
            目标函数值
        """
        # 和值一致性损失
        sum_loss = (sum_pred - expected_sum) ** 2
        
        # 位置约束损失
        position_loss = 0
        h_pred = position_predictions.get('hundreds_pred', 5)
        t_pred = position_predictions.get('tens_pred', 5)
        u_pred = position_predictions.get('units_pred', 5)
        
        predicted_combination_sum = h_pred + t_pred + u_pred
        position_loss = (sum_pred - predicted_combination_sum) ** 2
        
        # 加权组合
        total_loss = (self.constraint_params['sum_weight'] * sum_loss + 
                     self.constraint_params['position_weight'] * position_loss)
        
        return total_loss
    
    def optimize_sum_prediction(self, initial_sum: float, expected_sum: float,
                               position_predictions: Dict[str, float]) -> float:
        """
        优化和值预测
        
        Args:
            initial_sum: 初始和值预测
            expected_sum: 期望和值
            position_predictions: 位置预测
            
        Returns:
            优化后的和值预测
        """
        try:
            # 定义约束：和值必须在[0, 27]范围内
            bounds = [(0, 27)]
            
            # 优化目标函数
            result = minimize(
                fun=lambda x: self.constraint_objective(x[0], expected_sum, position_predictions),
                x0=[initial_sum],
                bounds=bounds,
                method='L-BFGS-B',
                options={'maxiter': self.constraint_params['max_iterations']}
            )
            
            if result.success:
                return result.x[0]
            else:
                # 如果优化失败，返回加权平均
                weight = self.constraint_params['sum_weight']
                return weight * initial_sum + (1 - weight) * expected_sum
                
        except Exception as e:
            self.logger.warning(f"约束优化失败: {e}")
            # 返回简单加权平均
            weight = self.constraint_params['sum_weight']
            return weight * initial_sum + (1 - weight) * expected_sum
    
    def build_model(self):
        """构建基础回归模型"""
        try:
            import xgboost as xgb
            
            # 使用XGBoost作为基础模型
            self.model = xgb.XGBRegressor(
                objective='reg:squarederror',
                max_depth=6,
                learning_rate=0.1,
                n_estimators=100,
                random_state=42,
                n_jobs=-1
            )
            
            self.logger.info("约束优化和值模型构建成功")
            return self.model
            
        except ImportError:
            error_msg = "XGBoost未安装，请安装: pip install xgboost"
            self.logger.error(error_msg)
            raise ImportError(error_msg)
    
    def prepare_features(self, data: pd.DataFrame, window_size: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练特征"""
        # 计算和值
        data['sum_value'] = data['hundreds'] + data['tens'] + data['units']
        
        X = []
        y = []
        
        # 构建滑动窗口特征
        for i in range(window_size, len(data)):
            features = []
            
            # 历史和值特征
            recent_sums = data.iloc[i - window_size:i]['sum_value']
            features.extend([
                recent_sums.mean(),
                recent_sums.std(),
                recent_sums.min(),
                recent_sums.max(),
                recent_sums.median()
            ])
            
            # 位置相关特征
            recent_hundreds = data.iloc[i - window_size:i]['hundreds']
            recent_tens = data.iloc[i - window_size:i]['tens']
            recent_units = data.iloc[i - window_size:i]['units']
            
            features.extend([
                recent_hundreds.mean(),
                recent_tens.mean(),
                recent_units.mean(),
                recent_hundreds.std(),
                recent_tens.std(),
                recent_units.std()
            ])
            
            # 约束相关特征
            position_sum_consistency = []
            for j in range(len(recent_sums)):
                h = recent_hundreds.iloc[j]
                t = recent_tens.iloc[j]
                u = recent_units.iloc[j]
                actual_sum = recent_sums.iloc[j]
                expected_sum = h + t + u
                consistency = 1.0 - abs(actual_sum - expected_sum) / 27.0
                position_sum_consistency.append(consistency)
            
            features.append(np.mean(position_sum_consistency))
            
            X.append(features)
            y.append(data.iloc[i]['sum_value'])
        
        return np.array(X), np.array(y)
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """从数据库加载训练数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT hundreds, tens, units, issue, draw_date
                FROM lottery_data
                ORDER BY draw_date, issue
            """
            
            data = pd.read_sql_query(query, conn)
            conn.close()
            
            if data.empty:
                raise ValueError("没有找到彩票数据")
            
            self.logger.info(f"加载了 {len(data)} 条彩票数据")
            
            X, y = self.prepare_features(data)
            
            self.logger.info(f"生成了 {len(X)} 个训练样本，特征维度: {X.shape[1]}")
            
            return X, y
            
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            raise
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict[str, float]:
        """训练约束优化模型"""
        try:
            if X is None or y is None:
                X, y = self.load_data()
            
            if self.model is None:
                self.build_model()
            
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            self.logger.info(f"训练集大小: {len(X_train)}, 验证集大小: {len(X_val)}")
            
            # 训练基础模型
            self.model.fit(X_train, y_train)
            
            self.is_trained = True
            
            # 评估模型
            performance = self.evaluate(X_val, y_val)
            
            self.logger.info(f"约束优化和值模型训练完成，验证集性能: {performance}")
            
            return performance
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测和值（带约束优化）"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 基础预测
            base_predictions = self.model.predict(X)
            
            # 获取位置预测
            position_predictions = self.get_position_predictions(X)
            
            # 计算期望和值
            expected_sums = self.calculate_expected_sum(position_predictions)
            
            # 约束优化
            optimized_predictions = []
            for i in range(len(base_predictions)):
                position_pred = {
                    'hundreds_pred': position_predictions['hundreds_pred'][i],
                    'tens_pred': position_predictions['tens_pred'][i],
                    'units_pred': position_predictions['units_pred'][i]
                }
                
                optimized_sum = self.optimize_sum_prediction(
                    base_predictions[i],
                    expected_sums[i],
                    position_pred
                )
                
                optimized_predictions.append(optimized_sum)
            
            return np.array(optimized_predictions)
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def calculate_constraint_score(self, predicted_sum: float, 
                                 position_predictions: Dict[str, float]) -> float:
        """计算约束一致性分数"""
        h_pred = position_predictions.get('hundreds_pred', 5)
        t_pred = position_predictions.get('tens_pred', 5)
        u_pred = position_predictions.get('units_pred', 5)
        
        expected_sum = h_pred + t_pred + u_pred
        
        # 计算一致性分数（0-1之间）
        max_diff = 27  # 最大可能差异
        actual_diff = abs(predicted_sum - expected_sum)
        consistency_score = max(0, 1 - actual_diff / max_diff)
        
        return consistency_score
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """预测和值及置信度"""
        predictions = self.predict(X)
        position_predictions = self.get_position_predictions(X)
        
        confidences = []
        for i, pred in enumerate(predictions):
            position_pred = {
                'hundreds_pred': position_predictions['hundreds_pred'][i],
                'tens_pred': position_predictions['tens_pred'][i],
                'units_pred': position_predictions['units_pred'][i]
            }
            
            # 基于约束一致性计算置信度
            constraint_score = self.calculate_constraint_score(pred, position_pred)
            
            # 基于预测值合理性的置信度
            distance_score = max(0, 1 - abs(pred - 13.5) / 13.5)
            
            # 综合置信度
            confidence = 0.6 * constraint_score + 0.4 * distance_score
            confidence = max(0.1, min(0.9, confidence))
            
            confidences.append(confidence)
        
        return predictions, np.array(confidences)
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """评估模型性能"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            y_pred = self.predict(X_test)
            
            # 基础回归指标
            mae = mean_absolute_error(y_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            r2 = r2_score(y_test, y_pred)
            
            # 准确率指标
            accuracy_1 = np.mean(np.abs(y_test - y_pred) <= 1)
            accuracy_2 = np.mean(np.abs(y_test - y_pred) <= 2)
            
            # 约束一致性指标
            position_predictions = self.get_position_predictions(X_test)
            constraint_scores = []
            
            for i in range(len(y_pred)):
                position_pred = {
                    'hundreds_pred': position_predictions['hundreds_pred'][i],
                    'tens_pred': position_predictions['tens_pred'][i],
                    'units_pred': position_predictions['units_pred'][i]
                }
                score = self.calculate_constraint_score(y_pred[i], position_pred)
                constraint_scores.append(score)
            
            avg_constraint_score = np.mean(constraint_scores)
            
            return {
                'mae': mae,
                'rmse': rmse,
                'r2_score': r2,
                'accuracy_1': accuracy_1,
                'accuracy_2': accuracy_2,
                'accuracy': accuracy_1,
                'constraint_score': avg_constraint_score  # 专属指标
            }
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            raise
    
    def save_model(self, filepath: str) -> bool:
        """保存模型"""
        if not self.is_trained:
            self.logger.warning("模型尚未训练，无法保存")
            return False
        
        try:
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            model_data = {
                'model': self.model,
                'constraint_params': self.constraint_params,
                'is_trained': self.is_trained
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"模型保存成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """加载模型"""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.constraint_params = model_data['constraint_params']
            self.is_trained = model_data['is_trained']
            
            self.logger.info(f"模型加载成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False
