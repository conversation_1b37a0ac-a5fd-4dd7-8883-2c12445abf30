#!/usr/bin/env python3
"""
P9异常检测和自动恢复处理器

该模块实现P9闭环优化系统的异常检测和自动恢复功能，包括：
1. 系统异常检测和分类
2. 自动恢复策略执行
3. 告警升级机制
4. 异常历史记录和分析

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import sqlite3
import json
import logging
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
import threading
import time

class ExceptionSeverity(Enum):
    """异常严重程度枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ExceptionCategory(Enum):
    """异常类别枚举"""
    SYSTEM_ERROR = "system_error"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    DATA_QUALITY = "data_quality"
    RESOURCE_EXHAUSTION = "resource_exhaustion"
    INTEGRATION_FAILURE = "integration_failure"
    CONFIGURATION_ERROR = "configuration_error"

class RecoveryStatus(Enum):
    """恢复状态枚举"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    SUCCESSFUL = "successful"
    FAILED = "failed"
    MANUAL_REQUIRED = "manual_required"

@dataclass
class SystemException:
    """系统异常数据类"""
    exception_id: str
    category: ExceptionCategory
    severity: ExceptionSeverity
    component: str
    message: str
    details: Dict[str, Any]
    stack_trace: Optional[str] = None
    timestamp: datetime = None
    recovery_attempts: int = 0
    recovery_status: RecoveryStatus = RecoveryStatus.PENDING
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class RecoveryAction:
    """恢复动作数据类"""
    action_id: str
    action_type: str
    description: str
    execution_function: Callable
    max_attempts: int = 3
    timeout_seconds: int = 300
    prerequisites: List[str] = None
    
    def __post_init__(self):
        if self.prerequisites is None:
            self.prerequisites = []

class ExceptionHandler:
    """异常检测和自动恢复处理器"""
    
    def __init__(self, db_path: str, config: Optional[Dict[str, Any]] = None):
        """
        初始化异常处理器
        
        Args:
            db_path: 数据库路径
            config: 配置参数
        """
        self.db_path = db_path
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 异常处理配置
        self.handler_config = {
            'detection_interval': self.config.get('detection_interval', 60),
            'max_recovery_attempts': self.config.get('max_recovery_attempts', 3),
            'alert_threshold': self.config.get('alert_threshold', 5),
            'escalation_timeout': self.config.get('escalation_timeout', 1800),
            'auto_recovery_enabled': self.config.get('auto_recovery_enabled', True)
        }
        
        # 异常检测规则
        self.detection_rules = {
            'performance_degradation': {
                'threshold': 0.2,  # 性能下降20%
                'window_minutes': 15,
                'severity': ExceptionSeverity.MEDIUM
            },
            'error_rate_spike': {
                'threshold': 0.1,  # 错误率超过10%
                'window_minutes': 5,
                'severity': ExceptionSeverity.HIGH
            },
            'response_time_spike': {
                'threshold': 5.0,  # 响应时间超过5秒
                'window_minutes': 10,
                'severity': ExceptionSeverity.MEDIUM
            }
        }
        
        # 恢复策略
        self.recovery_strategies = {}
        self._initialize_recovery_strategies()
        
        # 异常历史
        self.exception_history = []
        
        # 监控线程
        self.monitoring_thread = None
        self.monitoring_active = False
        
        # 确保异常表存在
        self._ensure_exception_tables()
        
        self.logger.info("异常检测和自动恢复处理器初始化完成")
    
    def _ensure_exception_tables(self):
        """确保异常处理表存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查system_exceptions表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_exceptions'")
            if not cursor.fetchone():
                self.logger.warning("system_exceptions表不存在，将创建基本表结构")
                
                # 创建系统异常表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_exceptions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        exception_id TEXT UNIQUE NOT NULL,
                        category TEXT NOT NULL,
                        severity TEXT NOT NULL,
                        component TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT,
                        stack_trace TEXT,
                        recovery_attempts INTEGER DEFAULT 0,
                        recovery_status TEXT DEFAULT 'pending',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        resolved_at TIMESTAMP,
                        resolution_method TEXT
                    )
                """)
                
                # 创建恢复动作日志表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS recovery_actions_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        exception_id TEXT NOT NULL,
                        action_type TEXT NOT NULL,
                        action_description TEXT,
                        execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        status TEXT NOT NULL,
                        result_details TEXT,
                        execution_duration REAL
                    )
                """)
                
                conn.commit()
                self.logger.info("异常处理表创建成功")
            
            conn.close()
            
        except Exception as e:
            self.logger.error(f"确保异常处理表失败: {e}")
    
    def _initialize_recovery_strategies(self):
        """初始化恢复策略"""
        try:
            # 系统错误恢复策略
            self.recovery_strategies[ExceptionCategory.SYSTEM_ERROR] = [
                RecoveryAction(
                    action_id="restart_component",
                    action_type="component_restart",
                    description="重启故障组件",
                    execution_function=self._restart_component,
                    max_attempts=2
                ),
                RecoveryAction(
                    action_id="reset_configuration",
                    action_type="config_reset",
                    description="重置组件配置",
                    execution_function=self._reset_configuration,
                    max_attempts=1
                )
            ]
            
            # 性能下降恢复策略
            self.recovery_strategies[ExceptionCategory.PERFORMANCE_DEGRADATION] = [
                RecoveryAction(
                    action_id="optimize_parameters",
                    action_type="parameter_optimization",
                    description="优化系统参数",
                    execution_function=self._optimize_parameters,
                    max_attempts=2
                ),
                RecoveryAction(
                    action_id="clear_cache",
                    action_type="cache_clear",
                    description="清理系统缓存",
                    execution_function=self._clear_cache,
                    max_attempts=1
                )
            ]
            
            # 数据质量问题恢复策略
            self.recovery_strategies[ExceptionCategory.DATA_QUALITY] = [
                RecoveryAction(
                    action_id="refresh_data",
                    action_type="data_refresh",
                    description="刷新数据源",
                    execution_function=self._refresh_data,
                    max_attempts=2
                ),
                RecoveryAction(
                    action_id="validate_data",
                    action_type="data_validation",
                    description="验证数据完整性",
                    execution_function=self._validate_data,
                    max_attempts=1
                )
            ]
            
            # 资源耗尽恢复策略
            self.recovery_strategies[ExceptionCategory.RESOURCE_EXHAUSTION] = [
                RecoveryAction(
                    action_id="free_resources",
                    action_type="resource_cleanup",
                    description="释放系统资源",
                    execution_function=self._free_resources,
                    max_attempts=1
                ),
                RecoveryAction(
                    action_id="scale_resources",
                    action_type="resource_scaling",
                    description="扩展系统资源",
                    execution_function=self._scale_resources,
                    max_attempts=1
                )
            ]
            
            # 集成失败恢复策略
            self.recovery_strategies[ExceptionCategory.INTEGRATION_FAILURE] = [
                RecoveryAction(
                    action_id="reconnect_integration",
                    action_type="integration_reconnect",
                    description="重新连接集成组件",
                    execution_function=self._reconnect_integration,
                    max_attempts=3
                ),
                RecoveryAction(
                    action_id="fallback_mode",
                    action_type="fallback_activation",
                    description="激活备用模式",
                    execution_function=self._activate_fallback_mode,
                    max_attempts=1
                )
            ]
            
            # 配置错误恢复策略
            self.recovery_strategies[ExceptionCategory.CONFIGURATION_ERROR] = [
                RecoveryAction(
                    action_id="restore_default_config",
                    action_type="config_restore",
                    description="恢复默认配置",
                    execution_function=self._restore_default_config,
                    max_attempts=1
                ),
                RecoveryAction(
                    action_id="validate_config",
                    action_type="config_validation",
                    description="验证配置文件",
                    execution_function=self._validate_config,
                    max_attempts=1
                )
            ]
            
            self.logger.info("恢复策略初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化恢复策略失败: {e}")
    
    def detect_exceptions(self) -> List[SystemException]:
        """检测系统异常"""
        detected_exceptions = []
        
        try:
            # 检测性能下降
            perf_exceptions = self._detect_performance_degradation()
            detected_exceptions.extend(perf_exceptions)
            
            # 检测错误率飙升
            error_exceptions = self._detect_error_rate_spike()
            detected_exceptions.extend(error_exceptions)
            
            # 检测响应时间异常
            response_exceptions = self._detect_response_time_spike()
            detected_exceptions.extend(response_exceptions)
            
            # 检测资源使用异常
            resource_exceptions = self._detect_resource_exhaustion()
            detected_exceptions.extend(resource_exceptions)
            
            # 检测集成失败
            integration_exceptions = self._detect_integration_failures()
            detected_exceptions.extend(integration_exceptions)
            
            if detected_exceptions:
                self.logger.warning(f"检测到 {len(detected_exceptions)} 个系统异常")
            
            return detected_exceptions
            
        except Exception as e:
            self.logger.error(f"异常检测失败: {e}")
            return []

    def _detect_performance_degradation(self) -> List[SystemException]:
        """检测性能下降"""
        exceptions = []

        try:
            # 查询最近的性能数据
            conn = sqlite3.connect(self.db_path)

            window_minutes = self.detection_rules['performance_degradation']['window_minutes']
            threshold = self.detection_rules['performance_degradation']['threshold']

            query = """
                SELECT component_name, performance_metric, current_value, baseline_value
                FROM system_performance_monitor
                WHERE monitor_time > datetime('now', '-{} minutes')
                AND baseline_value IS NOT NULL
            """.format(window_minutes)

            cursor = conn.execute(query)
            results = cursor.fetchall()
            conn.close()

            for component, metric, current, baseline in results:
                if baseline > 0 and (baseline - current) / baseline > threshold:
                    exception = SystemException(
                        exception_id=f"perf_deg_{component}_{metric}_{int(datetime.now().timestamp())}",
                        category=ExceptionCategory.PERFORMANCE_DEGRADATION,
                        severity=self.detection_rules['performance_degradation']['severity'],
                        component=component,
                        message=f"{component}的{metric}性能下降{((baseline - current) / baseline * 100):.1f}%",
                        details={
                            'metric': metric,
                            'current_value': current,
                            'baseline_value': baseline,
                            'degradation_percentage': (baseline - current) / baseline * 100
                        }
                    )
                    exceptions.append(exception)

            return exceptions

        except Exception as e:
            self.logger.error(f"检测性能下降失败: {e}")
            return []

    def _detect_error_rate_spike(self) -> List[SystemException]:
        """检测错误率飙升"""
        exceptions = []

        try:
            # 查询最近的错误记录
            conn = sqlite3.connect(self.db_path)

            window_minutes = self.detection_rules['error_rate_spike']['window_minutes']
            threshold = self.detection_rules['error_rate_spike']['threshold']

            query = """
                SELECT component_name, COUNT(*) as total_count,
                       SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as error_count
                FROM optimization_logs
                WHERE start_time > datetime('now', '-{} minutes')
                GROUP BY component_name
                HAVING total_count > 0
            """.format(window_minutes)

            cursor = conn.execute(query)
            results = cursor.fetchall()
            conn.close()

            for component, total, errors in results:
                error_rate = errors / total if total > 0 else 0

                if error_rate > threshold:
                    exception = SystemException(
                        exception_id=f"error_spike_{component}_{int(datetime.now().timestamp())}",
                        category=ExceptionCategory.SYSTEM_ERROR,
                        severity=self.detection_rules['error_rate_spike']['severity'],
                        component=component or 'unknown',
                        message=f"{component}错误率飙升至{error_rate*100:.1f}%",
                        details={
                            'error_rate': error_rate,
                            'total_operations': total,
                            'error_count': errors,
                            'window_minutes': window_minutes
                        }
                    )
                    exceptions.append(exception)

            return exceptions

        except Exception as e:
            self.logger.error(f"检测错误率飙升失败: {e}")
            return []

    def _detect_response_time_spike(self) -> List[SystemException]:
        """检测响应时间异常"""
        # 简化实现，返回空列表
        return []

    def _detect_resource_exhaustion(self) -> List[SystemException]:
        """检测资源耗尽"""
        # 简化实现，返回空列表
        return []

    def _detect_integration_failures(self) -> List[SystemException]:
        """检测集成失败"""
        # 简化实现，返回空列表
        return []

    def handle_exception(self, exception: SystemException) -> Dict[str, Any]:
        """处理系统异常"""
        try:
            self.logger.warning(f"处理异常: {exception.exception_id} - {exception.message}")

            # 记录异常
            self._record_exception(exception)

            # 检查是否启用自动恢复
            if not self.handler_config['auto_recovery_enabled']:
                return {
                    'exception_id': exception.exception_id,
                    'status': 'recorded',
                    'message': '自动恢复已禁用，异常已记录'
                }

            # 检查恢复尝试次数
            if exception.recovery_attempts >= self.handler_config['max_recovery_attempts']:
                return {
                    'exception_id': exception.exception_id,
                    'status': 'max_attempts_reached',
                    'message': '已达到最大恢复尝试次数'
                }

            # 执行恢复策略
            recovery_result = self._execute_recovery_strategy(exception)

            # 更新异常状态
            self._update_exception_status(exception.exception_id, recovery_result)

            return recovery_result

        except Exception as e:
            self.logger.error(f"处理异常失败: {e}")
            return {
                'exception_id': exception.exception_id,
                'status': 'error',
                'error': str(e)
            }

    def _execute_recovery_strategy(self, exception: SystemException) -> Dict[str, Any]:
        """执行恢复策略"""
        try:
            strategies = self.recovery_strategies.get(exception.category, [])

            if not strategies:
                return {
                    'status': 'no_strategy',
                    'message': f'没有针对{exception.category.value}的恢复策略'
                }

            # 尝试执行恢复动作
            for strategy in strategies:
                try:
                    self.logger.info(f"执行恢复动作: {strategy.description}")

                    start_time = datetime.now()
                    result = strategy.execution_function(exception)
                    end_time = datetime.now()

                    execution_duration = (end_time - start_time).total_seconds()

                    # 记录恢复动作
                    self._record_recovery_action(
                        exception.exception_id,
                        strategy,
                        'successful' if result.get('success', False) else 'failed',
                        result,
                        execution_duration
                    )

                    if result.get('success', False):
                        return {
                            'status': 'recovered',
                            'strategy': strategy.action_type,
                            'message': f'通过{strategy.description}成功恢复',
                            'details': result
                        }

                except Exception as e:
                    self.logger.error(f"恢复动作执行失败: {e}")
                    self._record_recovery_action(
                        exception.exception_id,
                        strategy,
                        'error',
                        {'error': str(e)},
                        0
                    )

            return {
                'status': 'recovery_failed',
                'message': '所有恢复策略都失败了'
            }

        except Exception as e:
            self.logger.error(f"执行恢复策略失败: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }

    # 恢复动作实现（简化版本）
    def _restart_component(self, exception: SystemException) -> Dict[str, Any]:
        """重启组件"""
        try:
            component = exception.component
            self.logger.info(f"模拟重启组件: {component}")

            # 这里应该实现实际的组件重启逻辑
            # 简化实现，直接返回成功

            return {
                'success': True,
                'message': f'组件{component}重启成功',
                'component': component
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _reset_configuration(self, exception: SystemException) -> Dict[str, Any]:
        """重置配置"""
        try:
            component = exception.component
            self.logger.info(f"模拟重置配置: {component}")

            return {
                'success': True,
                'message': f'组件{component}配置重置成功',
                'component': component
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _optimize_parameters(self, exception: SystemException) -> Dict[str, Any]:
        """优化参数"""
        try:
            component = exception.component
            self.logger.info(f"模拟优化参数: {component}")

            return {
                'success': True,
                'message': f'组件{component}参数优化成功',
                'component': component
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    # 其他恢复动作的简化实现
    def _clear_cache(self, exception: SystemException) -> Dict[str, Any]:
        """清理缓存"""
        return {'success': True, 'message': '缓存清理成功'}

    def _refresh_data(self, exception: SystemException) -> Dict[str, Any]:
        """刷新数据"""
        return {'success': True, 'message': '数据刷新成功'}

    def _validate_data(self, exception: SystemException) -> Dict[str, Any]:
        """验证数据"""
        return {'success': True, 'message': '数据验证成功'}

    def _free_resources(self, exception: SystemException) -> Dict[str, Any]:
        """释放资源"""
        return {'success': True, 'message': '资源释放成功'}

    def _scale_resources(self, exception: SystemException) -> Dict[str, Any]:
        """扩展资源"""
        return {'success': True, 'message': '资源扩展成功'}

    def _reconnect_integration(self, exception: SystemException) -> Dict[str, Any]:
        """重新连接集成"""
        return {'success': True, 'message': '集成重连成功'}

    def _activate_fallback_mode(self, exception: SystemException) -> Dict[str, Any]:
        """激活备用模式"""
        return {'success': True, 'message': '备用模式激活成功'}

    def _restore_default_config(self, exception: SystemException) -> Dict[str, Any]:
        """恢复默认配置"""
        return {'success': True, 'message': '默认配置恢复成功'}

    def _validate_config(self, exception: SystemException) -> Dict[str, Any]:
        """验证配置"""
        return {'success': True, 'message': '配置验证成功'}

    def get_exception_summary(self, hours_back: int = 24) -> Dict[str, Any]:
        """获取异常摘要"""
        try:
            conn = sqlite3.connect(self.db_path)

            query = """
                SELECT category, severity, recovery_status, COUNT(*) as count
                FROM system_exceptions
                WHERE created_at > datetime('now', '-{} hours')
                GROUP BY category, severity, recovery_status
            """.format(hours_back)

            cursor = conn.execute(query)
            results = cursor.fetchall()

            # 统计总数
            total_query = """
                SELECT COUNT(*) as total,
                       SUM(CASE WHEN recovery_status = 'successful' THEN 1 ELSE 0 END) as resolved,
                       SUM(CASE WHEN recovery_status = 'failed' THEN 1 ELSE 0 END) as failed
                FROM system_exceptions
                WHERE created_at > datetime('now', '-{} hours')
            """.format(hours_back)

            total_result = conn.execute(total_query).fetchone()
            conn.close()

            # 组织结果
            summary = {
                'total_exceptions': total_result[0] if total_result else 0,
                'resolved_exceptions': total_result[1] if total_result else 0,
                'failed_exceptions': total_result[2] if total_result else 0,
                'by_category': {},
                'by_severity': {},
                'by_status': {},
                'analysis_period_hours': hours_back,
                'timestamp': datetime.now().isoformat()
            }

            # 按类别、严重程度、状态分组
            for category, severity, status, count in results:
                if category not in summary['by_category']:
                    summary['by_category'][category] = 0
                summary['by_category'][category] += count

                if severity not in summary['by_severity']:
                    summary['by_severity'][severity] = 0
                summary['by_severity'][severity] += count

                if status not in summary['by_status']:
                    summary['by_status'][status] = 0
                summary['by_status'][status] += count

            return summary

        except Exception as e:
            self.logger.error(f"获取异常摘要失败: {e}")
            return {'error': str(e)}
