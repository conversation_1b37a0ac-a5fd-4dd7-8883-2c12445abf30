"""
P2高级特征工程系统 - API v2 高级特征接口

扩展现有API接口，支持高级特征获取、批量处理、特征重要性分析等功能。

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

from flask import Blueprint, request, jsonify, current_app
from functools import wraps
import time
import logging
from typing import Dict, List, Any, Optional
import traceback
import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

try:
    from src.data.advanced_feature_engineer import AdvancedFeatureEngineer
    from src.data.cache_optimizer import CacheOptimizer, CacheConfig
    from src.data.feature_importance import FeatureImportanceAnalyzer, AnalysisConfig
    from src.data.feature_service import FeatureService
except ImportError as e:
    print(f"导入模块失败: {e}")
    # 创建占位符类以避免运行时错误
    class AdvancedFeatureEngineer:
        def __init__(self, *args, **kwargs): pass
    class CacheOptimizer:
        def __init__(self, *args, **kwargs): pass
    class FeatureImportanceAnalyzer:
        def __init__(self, *args, **kwargs): pass
    class FeatureService:
        def __init__(self, *args, **kwargs): pass

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建蓝图
advanced_features_bp = Blueprint('advanced_features', __name__, url_prefix='/api/v2/features')

# 全局变量
_feature_engineer = None
_cache_optimizer = None
_importance_analyzer = None
_feature_service = None


def init_advanced_features_api(db_path: str = "data/lottery.db"):
    """
    初始化高级特征API
    
    Args:
        db_path: 数据库路径
    """
    global _feature_engineer, _cache_optimizer, _importance_analyzer, _feature_service
    
    try:
        # 初始化基础服务
        _feature_service = FeatureService(db_path)
        
        # 初始化高级特征工程器
        _feature_engineer = AdvancedFeatureEngineer(db_path, cache_enabled=True)
        
        # 初始化缓存优化器
        cache_config = CacheConfig(
            memory_size=1000,
            db_cache_enabled=True,
            db_cache_path="cache/api_cache.db"
        )
        _cache_optimizer = CacheOptimizer(cache_config)
        
        # 初始化特征重要性分析器
        analysis_config = AnalysisConfig(
            plot_enabled=False,
            save_plots=True,
            output_dir="analysis_output"
        )
        _importance_analyzer = FeatureImportanceAnalyzer(analysis_config)
        
        logger.info("高级特征API初始化完成")
        
    except Exception as e:
        logger.error(f"高级特征API初始化失败: {e}")
        raise


def api_error_handler(f):
    """API错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            start_time = time.time()
            result = f(*args, **kwargs)
            
            # 添加性能信息
            if isinstance(result, tuple) and len(result) == 2:
                response_data, status_code = result
            else:
                response_data = result
                status_code = 200
            
            if isinstance(response_data, dict):
                response_data['performance'] = {
                    'execution_time': round(time.time() - start_time, 3),
                    'timestamp': time.time()
                }
            
            return response_data, status_code
            
        except Exception as e:
            logger.error(f"API错误 {f.__name__}: {e}")
            logger.error(traceback.format_exc())
            
            return {
                'status': 'error',
                'message': str(e),
                'error_type': type(e).__name__,
                'timestamp': time.time()
            }, 500
    
    return decorated_function


def validate_issue(issue: str) -> bool:
    """验证期号格式"""
    if not issue or len(issue) != 7:
        return False
    
    try:
        year = int(issue[:4])
        number = int(issue[4:])
        return 2000 <= year <= 2030 and 1 <= number <= 365
    except ValueError:
        return False


@advanced_features_bp.route('/advanced/<feature_type>/<issue>', methods=['GET'])
@api_error_handler
def get_advanced_features(feature_type: str, issue: str):
    """
    获取指定期号的高级特征
    
    Args:
        feature_type: 特征类型 (hundreds, tens, units, sum, span, common, all)
        issue: 期号 (如: 2025001)
        
    Returns:
        JSON: 特征数据
    """
    # 验证参数
    if not validate_issue(issue):
        return {'status': 'error', 'message': '无效的期号格式'}, 400
    
    valid_types = ['hundreds', 'tens', 'units', 'sum', 'span', 'common', 'all']
    if feature_type not in valid_types:
        return {'status': 'error', 'message': f'无效的特征类型，支持: {valid_types}'}, 400
    
    if not _feature_engineer:
        return {'status': 'error', 'message': '特征工程器未初始化'}, 500
    
    # 获取特征
    features = _feature_engineer.get_features_with_cache(issue, feature_type)
    
    if features is None:
        return {'status': 'error', 'message': f'期号 {issue} 的特征数据不存在'}, 404
    
    # 获取缓存统计
    cache_stats = _feature_engineer.get_cache_stats()
    
    return {
        'status': 'success',
        'data': {
            'issue': issue,
            'feature_type': feature_type,
            'features': features,
            'feature_count': len(features),
            'cache_stats': cache_stats
        }
    }


@advanced_features_bp.route('/batch', methods=['POST'])
@api_error_handler
def get_batch_features():
    """
    批量获取多个期号的特征
    
    Request Body:
    {
        "issues": ["2025001", "2025002", "2025003"],
        "feature_type": "hundreds",
        "include_cache_stats": true
    }
    
    Returns:
        JSON: 批量特征数据
    """
    data = request.get_json()
    
    if not data:
        return {'status': 'error', 'message': '请求体不能为空'}, 400
    
    issues = data.get('issues', [])
    feature_type = data.get('feature_type', 'all')
    include_cache_stats = data.get('include_cache_stats', False)
    
    # 验证参数
    if not issues or not isinstance(issues, list):
        return {'status': 'error', 'message': '期号列表不能为空'}, 400
    
    if len(issues) > 100:
        return {'status': 'error', 'message': '批量请求期号数量不能超过100个'}, 400
    
    for issue in issues:
        if not validate_issue(issue):
            return {'status': 'error', 'message': f'无效的期号格式: {issue}'}, 400
    
    if not _feature_engineer:
        return {'status': 'error', 'message': '特征工程器未初始化'}, 500
    
    # 批量获取特征
    results = {}
    success_count = 0
    error_count = 0
    
    for issue in issues:
        try:
            features = _feature_engineer.get_features_with_cache(issue, feature_type)
            if features:
                results[issue] = {
                    'status': 'success',
                    'features': features,
                    'feature_count': len(features)
                }
                success_count += 1
            else:
                results[issue] = {
                    'status': 'error',
                    'message': '特征数据不存在'
                }
                error_count += 1
        except Exception as e:
            results[issue] = {
                'status': 'error',
                'message': str(e)
            }
            error_count += 1
    
    response_data = {
        'status': 'success',
        'data': {
            'feature_type': feature_type,
            'results': results,
            'summary': {
                'total_requested': len(issues),
                'success_count': success_count,
                'error_count': error_count,
                'success_rate': round(success_count / len(issues), 3)
            }
        }
    }
    
    # 添加缓存统计
    if include_cache_stats and _feature_engineer:
        response_data['data']['cache_stats'] = _feature_engineer.get_cache_stats()
    
    return response_data


@advanced_features_bp.route('/importance/<feature_type>', methods=['POST'])
@api_error_handler
def analyze_feature_importance(feature_type: str):
    """
    分析特征重要性
    
    Args:
        feature_type: 特征类型
        
    Request Body:
    {
        "issues": ["2025001", "2025002", ...],
        "target_variable": "hundreds",  # 目标变量类型
        "analysis_config": {
            "top_k_features": 20,
            "model_type": "auto"
        }
    }
    
    Returns:
        JSON: 特征重要性分析结果
    """
    data = request.get_json()
    
    if not data:
        return {'status': 'error', 'message': '请求体不能为空'}, 400
    
    issues = data.get('issues', [])
    target_variable = data.get('target_variable', 'hundreds')
    analysis_config = data.get('analysis_config', {})
    
    # 验证参数
    if not issues or len(issues) < 10:
        return {'status': 'error', 'message': '至少需要10个期号进行特征重要性分析'}, 400
    
    if not _importance_analyzer or not _feature_engineer:
        return {'status': 'error', 'message': '分析器未初始化'}, 500
    
    try:
        # 收集特征数据
        feature_data = []
        target_data = []
        
        for issue in issues:
            features = _feature_engineer.get_features_with_cache(issue, feature_type)
            if features:
                feature_data.append(features)

                # 获取真实的目标变量数据
                try:
                    lottery_data = _feature_service.get_lottery_data(issue)
                    if lottery_data:
                        if target_variable == 'hundreds':
                            target_value = lottery_data.get('hundreds', 0)
                        elif target_variable == 'tens':
                            target_value = lottery_data.get('tens', 0)
                        elif target_variable == 'units':
                            target_value = lottery_data.get('units', 0)
                        elif target_variable == 'sum':
                            h = lottery_data.get('hundreds', 0)
                            t = lottery_data.get('tens', 0)
                            u = lottery_data.get('units', 0)
                            target_value = h + t + u
                        elif target_variable == 'span':
                            h = lottery_data.get('hundreds', 0)
                            t = lottery_data.get('tens', 0)
                            u = lottery_data.get('units', 0)
                            target_value = max(h, t, u) - min(h, t, u)
                        else:
                            target_value = lottery_data.get('hundreds', 0)  # 默认使用百位

                        target_data.append(target_value)
                    else:
                        # 如果无法获取真实数据，跳过这个期号
                        feature_data.pop()  # 移除刚添加的特征数据
                except Exception as e:
                    logger.warning(f"获取期号{issue}目标变量失败: {e}")
                    feature_data.pop()  # 移除刚添加的特征数据
        
        if len(feature_data) < 10:
            return {'status': 'error', 'message': '有效特征数据不足，无法进行分析'}, 400
        
        # 转换为DataFrame
        import pandas as pd
        import numpy as np
        
        X = pd.DataFrame(feature_data)
        y = pd.Series(target_data)
        
        # 执行特征重要性分析
        result = _importance_analyzer.analyze_feature_importance(X, y)
        
        # 生成报告
        report = _importance_analyzer.generate_report(result)
        
        return {
            'status': 'success',
            'data': {
                'feature_type': feature_type,
                'target_variable': target_variable,
                'analysis_summary': report['analysis_summary'],
                'top_features': report['top_features']['top_10'],
                'feature_statistics': report['feature_statistics'],
                'model_performance': report['model_performance'],
                'recommendations': report['recommendations']
            }
        }
        
    except Exception as e:
        logger.error(f"特征重要性分析失败: {e}")
        return {'status': 'error', 'message': f'分析失败: {str(e)}'}, 500


@advanced_features_bp.route('/cache/stats', methods=['GET'])
@api_error_handler
def get_cache_stats():
    """
    获取缓存统计信息

    Returns:
        JSON: 缓存统计数据
    """
    if not _feature_engineer:
        return {'status': 'error', 'message': '特征工程器未初始化'}, 500

    # 获取特征工程器缓存统计
    fe_cache_stats = _feature_engineer.get_cache_stats()

    # 获取缓存优化器统计（如果可用）
    optimizer_stats = None
    if _cache_optimizer:
        try:
            optimizer_stats = _cache_optimizer.get_cache_info()
        except Exception as e:
            logger.warning(f"获取缓存优化器统计失败: {e}")

    return {
        'status': 'success',
        'data': {
            'feature_engineer_cache': fe_cache_stats,
            'cache_optimizer': optimizer_stats,
            'timestamp': time.time()
        }
    }


@advanced_features_bp.route('/cache/clear', methods=['POST'])
@api_error_handler
def clear_cache():
    """
    清理缓存

    Request Body:
    {
        "cache_type": "all"  # all, memory, db
    }

    Returns:
        JSON: 清理结果
    """
    data = request.get_json() or {}
    cache_type = data.get('cache_type', 'all')

    if cache_type not in ['all', 'memory', 'db']:
        return {'status': 'error', 'message': '无效的缓存类型'}, 400

    cleared_caches = []

    # 清理特征工程器缓存
    if _feature_engineer:
        try:
            _feature_engineer.clear_cache()
            cleared_caches.append('feature_engineer')
        except Exception as e:
            logger.error(f"清理特征工程器缓存失败: {e}")

    # 清理缓存优化器缓存
    if _cache_optimizer:
        try:
            _cache_optimizer.clear_cache(cache_type)
            cleared_caches.append('cache_optimizer')
        except Exception as e:
            logger.error(f"清理缓存优化器缓存失败: {e}")

    return {
        'status': 'success',
        'data': {
            'cache_type': cache_type,
            'cleared_caches': cleared_caches,
            'timestamp': time.time()
        }
    }


@advanced_features_bp.route('/available_types', methods=['GET'])
@api_error_handler
def get_available_feature_types():
    """
    获取可用的特征类型

    Returns:
        JSON: 可用特征类型列表
    """
    if not _feature_engineer:
        return {'status': 'error', 'message': '特征工程器未初始化'}, 500

    try:
        available_types = _feature_engineer.get_available_feature_types()

        return {
            'status': 'success',
            'data': {
                'feature_types': available_types,
                'total_types': len(available_types),
                'timestamp': time.time()
            }
        }
    except Exception as e:
        return {'status': 'error', 'message': f'获取特征类型失败: {str(e)}'}, 500


@advanced_features_bp.route('/health', methods=['GET'])
@api_error_handler
def health_check():
    """
    健康检查端点

    Returns:
        JSON: 系统健康状态
    """
    health_status = {
        'feature_engineer': _feature_engineer is not None,
        'cache_optimizer': _cache_optimizer is not None,
        'importance_analyzer': _importance_analyzer is not None,
        'feature_service': _feature_service is not None
    }

    all_healthy = all(health_status.values())

    return {
        'status': 'healthy' if all_healthy else 'degraded',
        'data': {
            'components': health_status,
            'overall_health': all_healthy,
            'timestamp': time.time()
        }
    }, 200 if all_healthy else 503


@advanced_features_bp.route('/export', methods=['POST'])
@api_error_handler
def export_features():
    """
    导出特征数据

    Request Body:
    {
        "issues": ["2025001", "2025002"],
        "feature_types": ["hundreds", "tens"],
        "format": "json",  # json, csv
        "include_metadata": true
    }

    Returns:
        JSON: 导出的特征数据
    """
    data = request.get_json()

    if not data:
        return {'status': 'error', 'message': '请求体不能为空'}, 400

    issues = data.get('issues', [])
    feature_types = data.get('feature_types', ['all'])
    export_format = data.get('format', 'json')
    include_metadata = data.get('include_metadata', True)

    # 验证参数
    if not issues:
        return {'status': 'error', 'message': '期号列表不能为空'}, 400

    if export_format not in ['json', 'csv']:
        return {'status': 'error', 'message': '不支持的导出格式'}, 400

    if not _feature_engineer:
        return {'status': 'error', 'message': '特征工程器未初始化'}, 500

    try:
        export_data = {}

        for issue in issues:
            if not validate_issue(issue):
                continue

            issue_data = {}
            for feature_type in feature_types:
                features = _feature_engineer.get_features_with_cache(issue, feature_type)
                if features:
                    issue_data[feature_type] = features

            if issue_data:
                export_data[issue] = issue_data

        response_data = {
            'status': 'success',
            'data': {
                'features': export_data,
                'export_info': {
                    'total_issues': len(export_data),
                    'feature_types': feature_types,
                    'format': export_format,
                    'export_time': time.time()
                }
            }
        }

        # 添加元数据
        if include_metadata:
            response_data['data']['metadata'] = {
                'api_version': 'v2',
                'feature_engineer_version': 'P2',
                'cache_stats': _feature_engineer.get_cache_stats()
            }

        return response_data

    except Exception as e:
        return {'status': 'error', 'message': f'导出失败: {str(e)}'}, 500


# 错误处理
@advanced_features_bp.errorhandler(404)
def not_found(error):
    return {
        'status': 'error',
        'message': '请求的资源不存在',
        'error_code': 404,
        'timestamp': time.time()
    }, 404


@advanced_features_bp.errorhandler(405)
def method_not_allowed(error):
    return {
        'status': 'error',
        'message': '不支持的HTTP方法',
        'error_code': 405,
        'timestamp': time.time()
    }, 405


@advanced_features_bp.errorhandler(500)
def internal_error(error):
    return {
        'status': 'error',
        'message': '服务器内部错误',
        'error_code': 500,
        'timestamp': time.time()
    }, 500


# 工具函数
def get_api_info():
    """获取API信息"""
    return {
        'name': 'Advanced Features API',
        'version': 'v2.0',
        'description': 'P2高级特征工程系统API接口',
        'endpoints': {
            'GET /api/v2/features/advanced/<feature_type>/<issue>': '获取单个期号的高级特征',
            'POST /api/v2/features/batch': '批量获取多个期号的特征',
            'POST /api/v2/features/importance/<feature_type>': '分析特征重要性',
            'GET /api/v2/features/cache/stats': '获取缓存统计信息',
            'POST /api/v2/features/cache/clear': '清理缓存',
            'GET /api/v2/features/available_types': '获取可用特征类型',
            'GET /api/v2/features/health': '健康检查',
            'POST /api/v2/features/export': '导出特征数据'
        },
        'supported_feature_types': [
            'hundreds', 'tens', 'units', 'sum', 'span', 'common', 'all'
        ]
    }


# 注册信息端点
@advanced_features_bp.route('/info', methods=['GET'])
@api_error_handler
def api_info():
    """API信息端点"""
    return {
        'status': 'success',
        'data': get_api_info()
    }
