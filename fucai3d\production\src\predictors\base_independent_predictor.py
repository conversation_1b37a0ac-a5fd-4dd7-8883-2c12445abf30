#!/usr/bin/env python3
"""
独立位置预测器基类

基于独立位置预测理念设计的抽象基类，为P3、P4、P5提供统一的接口和功能。

设计原则：
- 每个位置作为完全独立的随机变量进行预测
- 基于P2系统的特征工程和缓存优化
- 统一的接口和性能标准
- 支持多种机器学习模型

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
import os
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
import pandas as pd
import logging
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入P2系统组件
try:
    from src.interfaces.predictor_feature_interface import PredictorFeatureInterface, FeatureConfig
    from src.data.cache_optimizer import CacheOptimizer, CacheConfig
except ImportError as e:
    print(f"警告: 无法导入P2系统组件: {e}")
    print("请确保P2系统已正确安装和配置")

# 导入配置加载器
try:
    from config.config_loader import get_config, setup_logging
except ImportError:
    print("警告: 无法导入配置加载器，将使用默认配置")

class BaseIndependentPredictor(ABC):
    """
    独立位置预测器基类
    
    设计理念：
    - 每个位置完全独立预测，不依赖其他位置信息
    - 专注于单位置特征的深度优化
    - 避免复杂的关联性分析，确保预测稳定性
    - 基于P2系统的特征工程和缓存优化
    """
    
    def __init__(self, position: str, db_path: str):
        """
        初始化独立预测器
        
        Args:
            position: 位置类型 ('hundreds', 'tens', 'units')
            db_path: 数据库路径
        """
        self.position = position
        self.db_path = db_path
        self.model = None
        self.is_trained = False
        self.feature_names = []
        self.training_history = {}
        
        # 设置日志
        self._setup_logging()
        
        # 加载配置
        self._load_config()
        
        # 初始化P2系统组件
        self._init_p2_components()
        
        self.logger.info(f"初始化{position}位独立预测器")
    
    def _setup_logging(self):
        """设置日志"""
        try:
            setup_logging()
            self.logger = logging.getLogger(f"{self.position.title()}Predictor")
        except:
            # 如果配置加载失败，使用基础日志配置
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger(f"{self.position.title()}Predictor")
    
    def _load_config(self):
        """加载配置"""
        try:
            self.config_loader = get_config()
            self.predictor_config = self.config_loader.load_predictor_config()
            self.data_config = self.config_loader.get_data_config()
            self.training_config = self.config_loader.get_training_config()
            self.cache_config = self.config_loader.get_cache_config()
        except Exception as e:
            self.logger.warning(f"配置加载失败，使用默认配置: {e}")
            self._set_default_config()
    
    def _set_default_config(self):
        """设置默认配置"""
        self.data_config = {
            'feature_types': [self.position, 'common'],
            'window_size': 20,
            'lag_features': [1, 2, 3, 5, 7],
            'validation_split': 0.2,
            'max_training_samples': 8359
        }
        self.training_config = {
            'save_model': True,
            'model_save_path': f'models/{self.position}/'
        }
        self.cache_config = {
            'memory_size': 200,
            'db_cache_enabled': True
        }
    
    def _init_p2_components(self):
        """初始化P2系统组件"""
        try:
            # 初始化特征接口
            self.feature_interface = PredictorFeatureInterface(self.db_path, self.position)
            self.logger.info(f"P2特征接口初始化成功: {self.position}")
            
            # 配置缓存优化器
            cache_config = CacheConfig(
                memory_size=self.cache_config.get('memory_size', 200),
                db_cache_enabled=self.cache_config.get('db_cache_enabled', True),
                db_cache_path=f"cache/{self.position}_predictor_cache.db"
            )
            self.cache_optimizer = CacheOptimizer(cache_config)
            self.logger.info(f"缓存优化器初始化成功: {self.position}")
            
        except Exception as e:
            self.logger.error(f"P2系统组件初始化失败: {e}")
            self.feature_interface = None
            self.cache_optimizer = None
    
    @abstractmethod
    def build_model(self) -> Any:
        """
        构建模型
        
        Returns:
            构建好的模型对象
        """
        pass
    
    @abstractmethod
    def train(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            X: 特征矩阵
            y: 目标向量
            
        Returns:
            训练结果字典
        """
        pass
    
    @abstractmethod
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """
        预测概率分布（独立预测，无依赖）
        
        Args:
            X: 特征矩阵
            
        Returns:
            概率分布数组，shape: (n_samples, 10)
        """
        pass
    
    def load_training_data(self, limit: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        基于P2系统加载训练数据（独立加载）
        
        Args:
            limit: 限制训练样本数量，None表示使用全部数据
            
        Returns:
            X: 特征矩阵
            y: 目标向量（位置数字）
        """
        if self.feature_interface is None:
            raise RuntimeError("P2特征接口未初始化，无法加载训练数据")
        
        try:
            # 使用P2系统的特征配置
            config = FeatureConfig(
                feature_types=self.data_config.get('feature_types', [self.position, 'common']),
                window_size=self.data_config.get('window_size', 20),
                lag_features=self.data_config.get('lag_features', [1, 2, 3, 5, 7]),
                feature_selection=True,
                normalization="standard"
            )
            
            # 获取历史期号
            import sqlite3
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = "SELECT issue FROM lottery_data ORDER BY issue"
            if limit:
                query += f" LIMIT {limit}"
                
            cursor.execute(query)
            issues = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            self.logger.info(f"加载 {len(issues)} 期数据进行训练")
            
            # 使用P2系统创建ML数据集
            dataset = self.feature_interface.create_ml_pipeline(issues, config)
            
            # 保存特征名称
            self.feature_names = dataset.feature_names
            
            self.logger.info(f"特征矩阵形状: {dataset.X.shape}")
            self.logger.info(f"目标向量形状: {dataset.y.shape}")
            self.logger.info(f"特征数量: {len(dataset.feature_names)}")
            
            return dataset.X.values, dataset.y.values
            
        except Exception as e:
            self.logger.error(f"加载训练数据失败: {e}")
            raise
    
    def predict_next_period(self, current_issue: str) -> Dict[str, Any]:
        """
        预测下一期的概率分布
        
        Args:
            current_issue: 当前期号
            
        Returns:
            预测结果字典
        """
        if not self.is_trained:
            raise ValueError(f"{self.position}位预测器尚未训练")
        
        try:
            start_time = time.time()
            
            # 获取当前期特征（使用缓存）
            cache_key = f"{self.position}_features_{current_issue}"
            
            if self.cache_optimizer:
                cached_features = self.cache_optimizer.get_cached_features(cache_key)
                if cached_features:
                    features = cached_features
                    self.logger.debug(f"使用缓存特征: {current_issue}")
                else:
                    features = self._get_prediction_features(current_issue)
                    self.cache_optimizer.cache_features(cache_key, features)
            else:
                features = self._get_prediction_features(current_issue)
            
            # 预测概率分布
            X = np.array(features).reshape(1, -1)
            probabilities = self.predict_probability(X)[0]
            
            # 获取最高概率的数字
            predicted_digit = np.argmax(probabilities)
            confidence = probabilities[predicted_digit]
            
            # 获取Top3预测
            top3_indices = np.argsort(probabilities)[-3:][::-1]
            top3_predictions = [(int(idx), float(probabilities[idx])) for idx in top3_indices]
            
            prediction_time = time.time() - start_time
            
            result = {
                'position': self.position,
                'issue': current_issue,
                'probabilities': probabilities.tolist(),
                'predicted_digit': int(predicted_digit),
                'confidence': float(confidence),
                'top3_predictions': top3_predictions,
                'prediction_time': prediction_time,
                'feature_count': len(features),
                'model_type': self.__class__.__name__
            }
            
            self.logger.info(f"预测完成: {current_issue}, 预测数字: {predicted_digit}, 置信度: {confidence:.3f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def _get_prediction_features(self, issue: str) -> List[float]:
        """获取预测特征"""
        if self.feature_interface is None:
            raise RuntimeError("P2特征接口未初始化")
        
        # 这里应该调用P2系统的特征生成方法
        # 暂时返回模拟特征
        return [0.0] * len(self.feature_names) if self.feature_names else [0.0] * 50
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'position': self.position,
            'model_type': self.__class__.__name__,
            'is_trained': self.is_trained,
            'feature_count': len(self.feature_names),
            'training_history': self.training_history
        }
    
    def save_model(self, filepath: Optional[str] = None) -> str:
        """
        保存模型
        
        Args:
            filepath: 保存路径，None则使用默认路径
            
        Returns:
            实际保存路径
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，无法保存")
        
        if filepath is None:
            model_dir = Path(self.training_config.get('model_save_path', f'models/{self.position}/'))
            model_dir.mkdir(parents=True, exist_ok=True)
            filepath = model_dir / f"{self.__class__.__name__}_{self.position}.pkl"
        
        # 子类需要实现具体的保存逻辑
        self.logger.info(f"模型保存到: {filepath}")
        return str(filepath)
    
    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            是否加载成功
        """
        try:
            # 子类需要实现具体的加载逻辑
            self.is_trained = True
            self.logger.info(f"模型加载成功: {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False
