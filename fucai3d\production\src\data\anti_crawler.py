#!/usr/bin/env python3
"""
反爬虫突破模块
使用Playwright绕过反爬虫机制，获取福彩3D数据
"""

import asyncio
import logging
import tempfile
import os
from typing import Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class AntiCrawlerFetcher:
    """反爬虫数据获取器"""
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
        ]
    
    async def fetch_data_async(self, url: str) -> Optional[str]:
        """异步获取数据"""
        try:
            from playwright.async_api import async_playwright
            
            async with async_playwright() as p:
                # 使用Chromium浏览器
                browser = await p.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--disable-gpu'
                    ]
                )
                
                # 创建上下文，模拟真实浏览器
                context = await browser.new_context(
                    user_agent=self.user_agents[0],
                    viewport={'width': 1920, 'height': 1080},
                    locale='zh-CN',
                    timezone_id='Asia/Shanghai'
                )
                
                page = await context.new_page()
                
                try:
                    # 设置额外的请求头
                    await page.set_extra_http_headers({
                        'Accept': 'text/plain,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    })
                    
                    # 访问页面
                    logger.info(f"🔄 使用Playwright访问: {url}")
                    response = await page.goto(url, wait_until="networkidle", timeout=30000)
                    
                    if response.status != 200:
                        logger.error(f"❌ HTTP状态码: {response.status}")
                        return None
                    
                    # 等待页面加载完成
                    await page.wait_for_timeout(2000)
                    
                    # 获取页面文本内容
                    content = await page.evaluate("() => document.body.innerText")
                    
                    if content and len(content) > 1000:
                        logger.info(f"✅ Playwright获取成功，内容长度: {len(content)} 字符")
                        return content
                    else:
                        logger.warning(f"⚠️ 获取的内容太短: {len(content) if content else 0} 字符")
                        return None
                        
                except Exception as e:
                    logger.error(f"❌ 页面访问失败: {e}")
                    return None
                finally:
                    await browser.close()
                    
        except ImportError:
            logger.error("❌ Playwright未安装，请运行: pip install playwright && playwright install chromium")
            return None
        except Exception as e:
            logger.error(f"❌ Playwright获取失败: {e}")
            return None
    
    def fetch_data_sync(self, url: str) -> Optional[str]:
        """同步获取数据"""
        try:
            # 在新的事件循环中运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.fetch_data_async(url))
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"❌ 同步调用失败: {e}")
            return None
    
    def test_access(self, url: str) -> bool:
        """测试URL访问性"""
        content = self.fetch_data_sync(url)
        if content:
            lines = content.split('\n')
            logger.info(f"📊 测试结果: {len(content)} 字符, {len(lines)} 行")
            logger.info(f"📝 前3行预览: {lines[:3]}")
            return True
        return False


def test_anti_crawler():
    """测试反爬虫功能"""
    print("🧪 测试反爬虫突破功能")
    print("=" * 50)
    
    fetcher = AntiCrawlerFetcher()
    
    # 测试两个数据源
    urls = [
        "https://data.17500.cn/3d_asc.txt",
        "https://data.17500.cn/3d_desc.txt"
    ]
    
    for url in urls:
        print(f"\n🔍 测试: {url}")
        success = fetcher.test_access(url)
        print(f"结果: {'✅ 成功' if success else '❌ 失败'}")


if __name__ == "__main__":
    test_anti_crawler()
