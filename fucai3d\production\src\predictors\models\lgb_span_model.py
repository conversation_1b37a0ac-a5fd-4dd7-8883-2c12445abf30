"""
P7跨度预测器 - LightGBM回归模型

实现LightGBM回归模型，快速训练和预测
与XGBSpanModel类似的接口和功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
import pickle
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    lgb = None

from .base_span_model import BaseSpanModel

class LGBSpanModel(BaseSpanModel):
    """LightGBM跨度回归模型"""
    
    def __init__(self, db_path: str, config_path: Optional[str] = None):
        """
        初始化LightGBM跨度模型
        
        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        super().__init__(db_path, config_path)
        
        if not LIGHTGBM_AVAILABLE:
            raise ImportError("LightGBM未安装，请运行: pip install lightgbm")
        
        self.model_type = 'lgb'
        self.model = None
        self.feature_names = None
        self.is_trained = False
        
        # 加载LightGBM配置
        self.lgb_config = self.config.get('span_predictor', {}).get('models', {}).get('lgb', {})
        
        # 默认参数
        self.default_params = {
            'n_estimators': 200,
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': 42,
            'n_jobs': -1,
            'early_stopping_rounds': 20,
            'metric': 'rmse',
            'verbose': -1
        }
        
        # 合并配置参数
        self.params = {**self.default_params, **self.lgb_config}
        
        self.logger.info(f"LightGBM跨度模型初始化完成，参数: {self.params}")
    
    def build_model(self) -> bool:
        """
        构建LightGBM模型
        
        Returns:
            是否构建成功
        """
        try:
            # 创建LightGBM回归器
            self.model = lgb.LGBMRegressor(
                n_estimators=self.params['n_estimators'],
                max_depth=self.params['max_depth'],
                learning_rate=self.params['learning_rate'],
                subsample=self.params['subsample'],
                colsample_bytree=self.params['colsample_bytree'],
                random_state=self.params['random_state'],
                n_jobs=self.params['n_jobs'],
                verbose=self.params['verbose']
            )
            
            self.logger.info("LightGBM跨度模型构建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"LightGBM跨度模型构建失败: {e}")
            return False
    
    def prepare_features(self, data: pd.DataFrame) -> np.ndarray:
        """
        准备特征数据（与XGBSpanModel相同的特征工程）
        
        Args:
            data: 原始数据
            
        Returns:
            特征矩阵
        """
        features = []
        
        # 基础特征：历史跨度
        if 'span' in data.columns:
            features.append(data['span'].values.reshape(-1, 1))
        
        # 位置特征
        if all(col in data.columns for col in ['hundreds', 'tens', 'units']):
            position_features = data[['hundreds', 'tens', 'units']].values
            features.append(position_features)
        
        # 和值特征
        if all(col in data.columns for col in ['hundreds', 'tens', 'units']):
            sum_values = (data['hundreds'] + data['tens'] + data['units']).values.reshape(-1, 1)
            features.append(sum_values)
        
        # 滞后特征
        if 'span' in data.columns:
            for lag in [1, 2, 3, 5, 7]:
                lag_feature = data['span'].shift(lag).fillna(data['span'].mean()).values.reshape(-1, 1)
                features.append(lag_feature)
        
        # 滚动统计特征
        if 'span' in data.columns:
            for window in [3, 5, 7]:
                rolling_mean = data['span'].rolling(window=window).mean().fillna(data['span'].mean()).values.reshape(-1, 1)
                rolling_std = data['span'].rolling(window=window).std().fillna(0).values.reshape(-1, 1)
                features.extend([rolling_mean, rolling_std])
        
        # 模式特征
        if all(col in data.columns for col in ['hundreds', 'tens', 'units']):
            pattern_features = self._extract_pattern_features(data)
            features.append(pattern_features)
        
        # 合并所有特征
        if features:
            X = np.hstack(features)
            
            # 记录特征名称
            if self.feature_names is None:
                self.feature_names = self._generate_feature_names(X.shape[1])
            
            return X
        else:
            raise ValueError("无法提取有效特征")
    
    def _extract_pattern_features(self, data: pd.DataFrame) -> np.ndarray:
        """提取模式特征"""
        pattern_features = []
        
        for _, row in data.iterrows():
            digits = [row['hundreds'], row['tens'], row['units']]
            
            # 升序模式
            ascending = 1 if digits == sorted(digits) and len(set(digits)) == 3 else 0
            
            # 降序模式
            descending = 1 if digits == sorted(digits, reverse=True) and len(set(digits)) == 3 else 0
            
            # 相同数字模式
            same_digit = 1 if len(set(digits)) == 1 else 0
            
            # 连续数字模式
            sorted_digits = sorted(digits)
            consecutive = 1 if (len(set(digits)) == 3 and 
                              sorted_digits[1] - sorted_digits[0] == 1 and 
                              sorted_digits[2] - sorted_digits[1] == 1) else 0
            
            # 重复数字数量
            duplicate_count = 3 - len(set(digits))
            
            # LightGBM特有特征：数字方差
            digit_variance = np.var(digits)
            
            pattern_features.append([ascending, descending, same_digit, consecutive, duplicate_count, digit_variance])
        
        return np.array(pattern_features)
    
    def _generate_feature_names(self, num_features: int) -> List[str]:
        """生成特征名称"""
        names = []
        
        # 基础特征名称
        base_names = ['span', 'hundreds', 'tens', 'units', 'sum']
        
        # 滞后特征名称
        for lag in [1, 2, 3, 5, 7]:
            base_names.append(f'span_lag_{lag}')
        
        # 滚动统计特征名称
        for window in [3, 5, 7]:
            base_names.extend([f'span_rolling_mean_{window}', f'span_rolling_std_{window}'])
        
        # 模式特征名称
        pattern_names = ['ascending', 'descending', 'same_digit', 'consecutive', 'duplicate_count', 'digit_variance']
        base_names.extend(pattern_names)
        
        # 如果特征数量不匹配，补充通用名称
        while len(base_names) < num_features:
            base_names.append(f'feature_{len(base_names)}')
        
        return base_names[:num_features]
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载训练数据
        
        Returns:
            特征矩阵和目标向量
        """
        try:
            # 从数据库加载数据
            data = self.data_access.load_lottery_data()
            
            if data.empty:
                raise ValueError("数据库中没有数据")
            
            # 计算跨度
            data['span'] = data[['hundreds', 'tens', 'units']].max(axis=1) - data[['hundreds', 'tens', 'units']].min(axis=1)
            
            # 准备特征
            X = self.prepare_features(data)
            
            # 目标变量（跨度）
            y = data['span'].values
            
            # 确保数据长度一致
            min_length = min(len(X), len(y))
            X = X[:min_length]
            y = y[:min_length]
            
            self.logger.info(f"加载跨度数据: {len(X)} 个样本, {X.shape[1]} 个特征")
            return X, y
            
        except Exception as e:
            self.logger.error(f"加载跨度数据失败: {e}")
            raise
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            X: 特征矩阵（可选）
            y: 目标向量（可选）
            
        Returns:
            训练性能指标
        """
        try:
            # 如果没有提供数据，从数据库加载
            if X is None or y is None:
                X, y = self.load_data()
            
            # 构建模型
            if self.model is None:
                self.build_model()
            
            # 数据分割
            from sklearn.model_selection import train_test_split
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # 训练模型
            eval_set = [(X_val, y_val)]
            
            self.model.fit(
                X_train, y_train,
                eval_set=eval_set,
                callbacks=[lgb.early_stopping(self.params.get('early_stopping_rounds', 20))],
                eval_names=['valid']
            )
            
            self.is_trained = True
            
            # 评估性能
            performance = self.evaluate(X_val, y_val)
            
            self.logger.info(f"LightGBM跨度模型训练完成: {performance}")
            return performance
            
        except Exception as e:
            self.logger.error(f"LightGBM跨度模型训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        预测跨度
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测结果
        """
        if not self.is_trained or self.model is None:
            raise ValueError("模型尚未训练")
        
        try:
            predictions = self.model.predict(X)
            
            # 跨度范围约束 (0-9)
            predictions = np.clip(predictions, 0, 9)
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"LightGBM跨度预测失败: {e}")
            raise
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        带置信度的预测
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测结果和置信度
        """
        predictions = self.predict(X)
        
        # 基于特征重要性计算置信度
        if hasattr(self.model, 'feature_importances_'):
            feature_importance_sum = np.sum(self.model.feature_importances_)
            base_confidence = min(0.9, feature_importance_sum / len(self.model.feature_importances_))
        else:
            base_confidence = 0.75  # LightGBM默认稍高的置信度
        
        # 为每个预测生成置信度
        confidences = np.full(len(predictions), base_confidence)
        
        # 根据预测值调整置信度
        for i, pred in enumerate(predictions):
            if abs(pred - round(pred)) < 0.1:  # 接近整数
                confidences[i] = min(0.95, confidences[i] + 0.1)
            
            # LightGBM特有：根据预测值范围调整置信度
            if 2 <= pred <= 7:  # 常见跨度范围
                confidences[i] = min(0.9, confidences[i] + 0.05)
        
        return predictions, confidences
    
    def save_model(self, filepath: str) -> bool:
        """
        保存模型
        
        Args:
            filepath: 保存路径
            
        Returns:
            是否保存成功
        """
        if not self.is_trained or self.model is None:
            self.logger.warning("模型尚未训练，无法保存")
            return False
        
        try:
            # 确保目录存在
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存模型和元数据
            model_data = {
                'model': self.model,
                'feature_names': self.feature_names,
                'params': self.params,
                'model_type': self.model_type,
                'is_trained': self.is_trained
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"LightGBM跨度模型已保存到: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存LightGBM跨度模型失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            是否加载成功
        """
        try:
            if not Path(filepath).exists():
                self.logger.error(f"模型文件不存在: {filepath}")
                return False
            
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.feature_names = model_data.get('feature_names')
            self.params = model_data.get('params', self.params)
            self.is_trained = model_data.get('is_trained', True)
            
            self.logger.info(f"LightGBM跨度模型已从 {filepath} 加载")
            return True
            
        except Exception as e:
            self.logger.error(f"加载LightGBM跨度模型失败: {e}")
            return False
    
    def get_feature_importance(self) -> Optional[Dict[str, float]]:
        """
        获取特征重要性
        
        Returns:
            特征重要性字典
        """
        if not self.is_trained or self.model is None:
            return None
        
        try:
            if hasattr(self.model, 'feature_importances_'):
                importances = self.model.feature_importances_
                
                if self.feature_names and len(self.feature_names) == len(importances):
                    return dict(zip(self.feature_names, importances))
                else:
                    return {f'feature_{i}': imp for i, imp in enumerate(importances)}
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取特征重要性失败: {e}")
            return None
