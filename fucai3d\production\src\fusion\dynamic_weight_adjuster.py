#!/usr/bin/env python3
"""
动态权重调整器

根据历史表现自动调整融合权重
为P8智能交集融合系统提供自适应权重管理

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from datetime import datetime, timedelta
import sqlite3
import json
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class PerformanceRecord:
    """性能记录数据类"""
    predictor_name: str
    issue: str
    predicted_value: Any
    actual_value: Any
    accuracy: float
    confidence: float
    timestamp: datetime
    
class DynamicWeightAdjuster:
    """动态权重调整器"""
    
    def __init__(self, db_path: str, config: Dict[str, Any]):
        """
        初始化动态权重调整器
        
        Args:
            db_path: 数据库路径
            config: 配置参数
        """
        self.db_path = db_path
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 调整参数
        self.adjustment_params = {
            'learning_rate': config.get('learning_rate', 0.1),          # 学习率
            'decay_factor': config.get('decay_factor', 0.95),           # 衰减因子
            'min_weight': config.get('min_weight', 0.1),                # 最小权重
            'max_weight': config.get('max_weight', 2.0),                # 最大权重
            'evaluation_window': config.get('evaluation_window', 30),   # 评估窗口（天）
            'min_samples': config.get('min_samples', 10),               # 最小样本数
            'confidence_threshold': config.get('confidence_threshold', 0.6), # 置信度阈值
            'performance_threshold': config.get('performance_threshold', 0.3) # 性能阈值
        }
        
        # 初始权重
        self.initial_weights = {
            'hundreds': 1.0,
            'tens': 1.0,
            'units': 1.0,
            'sum': 0.8,
            'span': 0.6,
            'constraint': 0.4
        }
        
        # 当前权重
        self.current_weights = self.initial_weights.copy()
        
        # 性能历史
        self.performance_history = defaultdict(list)
        
        self.logger.info("动态权重调整器初始化完成")
    
    def update_weights(self, performance_data: List[PerformanceRecord]) -> Dict[str, float]:
        """
        根据性能数据更新权重
        
        Args:
            performance_data: 性能记录列表
            
        Returns:
            更新后的权重字典
        """
        try:
            # 更新性能历史
            self._update_performance_history(performance_data)
            
            # 计算性能指标
            performance_metrics = self._calculate_performance_metrics()
            
            # 调整权重
            new_weights = self._adjust_weights_based_on_performance(performance_metrics)
            
            # 应用约束
            constrained_weights = self._apply_weight_constraints(new_weights)
            
            # 更新当前权重
            self.current_weights = constrained_weights
            
            # 保存权重历史
            self._save_weight_history(constrained_weights)
            
            self.logger.info(f"权重更新完成: {constrained_weights}")
            return constrained_weights
            
        except Exception as e:
            self.logger.error(f"权重更新失败: {e}")
            return self.current_weights
    
    def get_adaptive_weights(self, context: Dict[str, Any]) -> Dict[str, float]:
        """
        根据上下文获取自适应权重
        
        Args:
            context: 上下文信息
            
        Returns:
            自适应权重字典
        """
        try:
            # 基础权重
            adaptive_weights = self.current_weights.copy()
            
            # 根据置信度调整
            if 'confidences' in context:
                confidence_adjustments = self._calculate_confidence_adjustments(context['confidences'])
                for name, adjustment in confidence_adjustments.items():
                    if name in adaptive_weights:
                        adaptive_weights[name] *= adjustment
            
            # 根据最近性能调整
            recent_performance = self._get_recent_performance()
            performance_adjustments = self._calculate_performance_adjustments(recent_performance)
            for name, adjustment in performance_adjustments.items():
                if name in adaptive_weights:
                    adaptive_weights[name] *= adjustment
            
            # 根据数据质量调整
            if 'data_quality' in context:
                quality_adjustments = self._calculate_quality_adjustments(context['data_quality'])
                for name, adjustment in quality_adjustments.items():
                    if name in adaptive_weights:
                        adaptive_weights[name] *= adjustment
            
            # 归一化权重
            normalized_weights = self._normalize_weights(adaptive_weights)
            
            return normalized_weights
            
        except Exception as e:
            self.logger.error(f"获取自适应权重失败: {e}")
            return self.current_weights
    
    def _update_performance_history(self, performance_data: List[PerformanceRecord]):
        """更新性能历史"""
        for record in performance_data:
            self.performance_history[record.predictor_name].append(record)
            
            # 限制历史记录长度
            max_history = self.adjustment_params['evaluation_window'] * 2
            if len(self.performance_history[record.predictor_name]) > max_history:
                self.performance_history[record.predictor_name] = \
                    self.performance_history[record.predictor_name][-max_history:]
    
    def _calculate_performance_metrics(self) -> Dict[str, Dict[str, float]]:
        """计算性能指标"""
        metrics = {}
        
        for predictor_name, records in self.performance_history.items():
            if len(records) < self.adjustment_params['min_samples']:
                continue
            
            # 获取最近的记录
            recent_records = self._get_recent_records(records)
            
            if not recent_records:
                continue
            
            # 计算各种指标
            accuracies = [r.accuracy for r in recent_records]
            confidences = [r.confidence for r in recent_records]
            
            metrics[predictor_name] = {
                'mean_accuracy': np.mean(accuracies),
                'accuracy_std': np.std(accuracies),
                'mean_confidence': np.mean(confidences),
                'confidence_std': np.std(confidences),
                'sample_count': len(recent_records),
                'trend': self._calculate_trend(accuracies),
                'stability': self._calculate_stability(accuracies),
                'calibration': self._calculate_calibration(accuracies, confidences)
            }
        
        return metrics
    
    def _get_recent_records(self, records: List[PerformanceRecord]) -> List[PerformanceRecord]:
        """获取最近的记录"""
        cutoff_date = datetime.now() - timedelta(days=self.adjustment_params['evaluation_window'])
        return [r for r in records if r.timestamp >= cutoff_date]
    
    def _calculate_trend(self, accuracies: List[float]) -> float:
        """计算准确率趋势"""
        if len(accuracies) < 3:
            return 0.0
        
        # 简单线性趋势
        x = np.arange(len(accuracies))
        y = np.array(accuracies)
        
        # 计算斜率
        slope = np.polyfit(x, y, 1)[0]
        return slope
    
    def _calculate_stability(self, accuracies: List[float]) -> float:
        """计算稳定性（1 - 变异系数）"""
        if len(accuracies) < 2:
            return 1.0
        
        mean_acc = np.mean(accuracies)
        std_acc = np.std(accuracies)
        
        if mean_acc == 0:
            return 0.0
        
        cv = std_acc / mean_acc  # 变异系数
        stability = 1.0 / (1.0 + cv)  # 稳定性
        
        return stability
    
    def _calculate_calibration(self, accuracies: List[float], confidences: List[float]) -> float:
        """计算校准度（置信度与准确率的一致性）"""
        if len(accuracies) != len(confidences) or len(accuracies) < 2:
            return 0.5
        
        # 计算置信度与准确率的相关性
        correlation = np.corrcoef(accuracies, confidences)[0, 1]
        
        # 处理NaN值
        if np.isnan(correlation):
            return 0.5
        
        # 转换为0-1范围
        calibration = (correlation + 1) / 2
        
        return calibration
    
    def _adjust_weights_based_on_performance(self, metrics: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """基于性能调整权重"""
        new_weights = self.current_weights.copy()
        learning_rate = self.adjustment_params['learning_rate']
        
        for predictor_name, metric in metrics.items():
            if predictor_name not in new_weights:
                continue
            
            current_weight = new_weights[predictor_name]
            
            # 计算性能分数
            performance_score = self._calculate_performance_score(metric)
            
            # 计算权重调整
            target_weight = current_weight * (1 + performance_score)
            weight_adjustment = (target_weight - current_weight) * learning_rate
            
            # 应用调整
            new_weights[predictor_name] = current_weight + weight_adjustment
        
        return new_weights
    
    def _calculate_performance_score(self, metric: Dict[str, float]) -> float:
        """计算性能分数"""
        # 基础分数：准确率
        base_score = metric['mean_accuracy'] - 0.5  # 相对于随机猜测的改进
        
        # 趋势调整
        trend_adjustment = metric['trend'] * 0.5
        
        # 稳定性调整
        stability_adjustment = (metric['stability'] - 0.5) * 0.3
        
        # 校准度调整
        calibration_adjustment = (metric['calibration'] - 0.5) * 0.2
        
        # 综合分数
        performance_score = base_score + trend_adjustment + stability_adjustment + calibration_adjustment
        
        # 限制分数范围
        return np.clip(performance_score, -0.5, 0.5)
    
    def _calculate_confidence_adjustments(self, confidences: Dict[str, float]) -> Dict[str, float]:
        """计算基于置信度的调整"""
        adjustments = {}
        
        for name, confidence in confidences.items():
            if confidence >= self.adjustment_params['confidence_threshold']:
                # 高置信度，增加权重
                adjustments[name] = 1.0 + (confidence - 0.5) * 0.2
            else:
                # 低置信度，减少权重
                adjustments[name] = 0.8 + confidence * 0.4
        
        return adjustments
    
    def _get_recent_performance(self) -> Dict[str, float]:
        """获取最近性能"""
        recent_performance = {}
        
        for predictor_name, records in self.performance_history.items():
            recent_records = self._get_recent_records(records)
            if recent_records:
                recent_performance[predictor_name] = np.mean([r.accuracy for r in recent_records])
        
        return recent_performance
    
    def _calculate_performance_adjustments(self, recent_performance: Dict[str, float]) -> Dict[str, float]:
        """计算基于性能的调整"""
        adjustments = {}
        
        if not recent_performance:
            return adjustments
        
        # 计算相对性能
        mean_performance = np.mean(list(recent_performance.values()))
        
        for name, performance in recent_performance.items():
            relative_performance = performance - mean_performance
            
            # 性能好的预测器增加权重，性能差的减少权重
            adjustments[name] = 1.0 + relative_performance * 0.5
        
        return adjustments
    
    def _calculate_quality_adjustments(self, data_quality: Dict[str, float]) -> Dict[str, float]:
        """计算基于数据质量的调整"""
        adjustments = {}
        
        for name, quality in data_quality.items():
            # 数据质量好的预测器增加权重
            adjustments[name] = 0.8 + quality * 0.4
        
        return adjustments
    
    def _apply_weight_constraints(self, weights: Dict[str, float]) -> Dict[str, float]:
        """应用权重约束"""
        constrained_weights = {}
        
        min_weight = self.adjustment_params['min_weight']
        max_weight = self.adjustment_params['max_weight']
        
        for name, weight in weights.items():
            constrained_weights[name] = np.clip(weight, min_weight, max_weight)
        
        return constrained_weights
    
    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        """归一化权重"""
        # 分组归一化
        position_weights = {k: v for k, v in weights.items() if k in ['hundreds', 'tens', 'units']}
        other_weights = {k: v for k, v in weights.items() if k not in ['hundreds', 'tens', 'units']}
        
        normalized_weights = {}
        
        # 归一化位置权重
        if position_weights:
            total_position = sum(position_weights.values())
            for name, weight in position_weights.items():
                normalized_weights[name] = weight / total_position * 3  # 保持总和为3
        
        # 其他权重保持原值
        normalized_weights.update(other_weights)
        
        return normalized_weights
    
    def _save_weight_history(self, weights: Dict[str, float]):
        """保存权重历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建权重历史表（如果不存在）
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS weight_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        weights TEXT NOT NULL,
                        performance_summary TEXT
                    )
                ''')
                
                # 插入权重记录
                cursor.execute('''
                    INSERT INTO weight_history (timestamp, weights)
                    VALUES (?, ?)
                ''', (datetime.now().isoformat(), json.dumps(weights)))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"保存权重历史失败: {e}")
    
    def load_weight_history(self, days: int = 30) -> List[Dict[str, Any]]:
        """加载权重历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
                
                cursor.execute('''
                    SELECT timestamp, weights, performance_summary
                    FROM weight_history
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                ''', (cutoff_date,))
                
                history = []
                for row in cursor.fetchall():
                    history.append({
                        'timestamp': row[0],
                        'weights': json.loads(row[1]),
                        'performance_summary': json.loads(row[2]) if row[2] else None
                    })
                
                return history
                
        except Exception as e:
            self.logger.error(f"加载权重历史失败: {e}")
            return []
    
    def reset_weights(self):
        """重置权重到初始值"""
        self.current_weights = self.initial_weights.copy()
        self.logger.info("权重已重置到初始值")
    
    def get_weight_summary(self) -> Dict[str, Any]:
        """获取权重摘要"""
        summary = {
            'current_weights': self.current_weights,
            'initial_weights': self.initial_weights,
            'weight_changes': {},
            'performance_summary': {}
        }
        
        # 计算权重变化
        for name in self.current_weights:
            if name in self.initial_weights:
                change = self.current_weights[name] - self.initial_weights[name]
                summary['weight_changes'][name] = {
                    'absolute_change': change,
                    'relative_change': change / self.initial_weights[name] if self.initial_weights[name] != 0 else 0
                }
        
        # 性能摘要
        for predictor_name, records in self.performance_history.items():
            if records:
                recent_records = self._get_recent_records(records)
                if recent_records:
                    summary['performance_summary'][predictor_name] = {
                        'sample_count': len(recent_records),
                        'mean_accuracy': np.mean([r.accuracy for r in recent_records]),
                        'mean_confidence': np.mean([r.confidence for r in recent_records])
                    }
        
        return summary
