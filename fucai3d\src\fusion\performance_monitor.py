#!/usr/bin/env python3
"""
性能监控系统

实现实时性能监控、指标计算和告警机制
为P8智能交集融合系统提供性能监控功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Callable
import logging
import threading
import time
import json
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque
import sqlite3

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    name: str
    value: float
    timestamp: datetime
    category: str
    threshold: Optional[float] = None
    status: str = "normal"  # normal/warning/critical

@dataclass
class AlertRule:
    """告警规则数据类"""
    name: str
    metric_name: str
    condition: str  # >, <, >=, <=, ==, !=
    threshold: float
    severity: str  # info/warning/critical
    enabled: bool = True

class PerformanceMonitor:
    """性能监控系统"""
    
    def __init__(self, db_path: str, config: Dict[str, Any]):
        """
        初始化性能监控系统
        
        Args:
            db_path: 数据库路径
            config: 监控配置
        """
        self.db_path = db_path
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 指标存储
        self.metrics_buffer = deque(maxlen=1000)
        self.metrics_history = defaultdict(list)
        
        # 告警规则
        self.alert_rules = self._load_alert_rules()
        
        # 告警回调
        self.alert_callbacks = []
        
        # 性能统计
        self.stats = {
            'total_predictions': 0,
            'successful_predictions': 0,
            'failed_predictions': 0,
            'total_execution_time': 0.0,
            'start_time': datetime.now()
        }
        
        self.logger.info("性能监控系统初始化完成")
    
    def _load_alert_rules(self) -> List[AlertRule]:
        """加载告警规则"""
        default_rules = [
            AlertRule("低成功率告警", "success_rate", "<", 0.8, "warning"),
            AlertRule("高执行时间告警", "avg_execution_time", ">", 5.0, "warning"),
            AlertRule("低命中率告警", "hit_rate", "<", 0.1, "critical"),
            AlertRule("系统错误告警", "error_rate", ">", 0.1, "critical"),
            AlertRule("内存使用告警", "memory_usage", ">", 0.8, "warning"),
            AlertRule("数据库连接告警", "db_connection_errors", ">", 5, "critical")
        ]
        
        # 从配置文件加载自定义规则
        custom_rules = self.config.get('alert_rules', [])
        for rule_config in custom_rules:
            rule = AlertRule(**rule_config)
            default_rules.append(rule)
        
        return default_rules
    
    def start_monitoring(self, interval: int = 60):
        """
        开始监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.is_monitoring:
            self.logger.warning("监控已经在运行")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info(f"性能监控已启动，间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("性能监控已停止")
    
    def _monitoring_loop(self, interval: int):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 收集性能指标
                metrics = self._collect_metrics()
                
                # 存储指标
                for metric in metrics:
                    self._store_metric(metric)
                
                # 检查告警
                self._check_alerts(metrics)
                
                # 等待下一次监控
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(interval)
    
    def _collect_metrics(self) -> List[PerformanceMetric]:
        """收集性能指标"""
        metrics = []
        current_time = datetime.now()
        
        try:
            # 系统基础指标
            metrics.extend(self._collect_system_metrics(current_time))
            
            # 预测性能指标
            metrics.extend(self._collect_prediction_metrics(current_time))
            
            # 数据库性能指标
            metrics.extend(self._collect_database_metrics(current_time))
            
            # 融合系统指标
            metrics.extend(self._collect_fusion_metrics(current_time))
            
        except Exception as e:
            self.logger.error(f"收集性能指标失败: {e}")
        
        return metrics
    
    def _collect_system_metrics(self, timestamp: datetime) -> List[PerformanceMetric]:
        """收集系统指标"""
        metrics = []
        
        try:
            import psutil
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            metrics.append(PerformanceMetric(
                "cpu_usage", cpu_percent, timestamp, "system", 80.0
            ))
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            metrics.append(PerformanceMetric(
                "memory_usage", memory_percent, timestamp, "system", 80.0
            ))
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            metrics.append(PerformanceMetric(
                "disk_usage", disk_percent, timestamp, "system", 90.0
            ))
            
        except ImportError:
            # 如果psutil不可用，使用简化指标
            metrics.append(PerformanceMetric(
                "system_status", 1.0, timestamp, "system"
            ))
        except Exception as e:
            self.logger.warning(f"收集系统指标失败: {e}")
        
        return metrics
    
    def _collect_prediction_metrics(self, timestamp: datetime) -> List[PerformanceMetric]:
        """收集预测性能指标"""
        metrics = []
        
        try:
            # 成功率
            total = self.stats['total_predictions']
            successful = self.stats['successful_predictions']
            success_rate = successful / total if total > 0 else 0.0
            
            metrics.append(PerformanceMetric(
                "success_rate", success_rate, timestamp, "prediction", 0.8
            ))
            
            # 错误率
            failed = self.stats['failed_predictions']
            error_rate = failed / total if total > 0 else 0.0
            
            metrics.append(PerformanceMetric(
                "error_rate", error_rate, timestamp, "prediction", 0.1
            ))
            
            # 平均执行时间
            avg_time = (self.stats['total_execution_time'] / total 
                       if total > 0 else 0.0)
            
            metrics.append(PerformanceMetric(
                "avg_execution_time", avg_time, timestamp, "prediction", 5.0
            ))
            
            # 预测频率（每小时）
            runtime = (datetime.now() - self.stats['start_time']).total_seconds() / 3600
            prediction_rate = total / runtime if runtime > 0 else 0.0
            
            metrics.append(PerformanceMetric(
                "prediction_rate", prediction_rate, timestamp, "prediction"
            ))
            
        except Exception as e:
            self.logger.warning(f"收集预测指标失败: {e}")
        
        return metrics
    
    def _collect_database_metrics(self, timestamp: datetime) -> List[PerformanceMetric]:
        """收集数据库性能指标"""
        metrics = []
        
        try:
            # 数据库连接测试
            connection_time = self._test_database_connection()
            
            metrics.append(PerformanceMetric(
                "db_connection_time", connection_time, timestamp, "database", 1.0
            ))
            
            # 数据库大小
            db_size = self._get_database_size()
            
            metrics.append(PerformanceMetric(
                "db_size_mb", db_size, timestamp, "database"
            ))
            
            # 最近的预测记录数
            recent_predictions = self._count_recent_predictions()
            
            metrics.append(PerformanceMetric(
                "recent_predictions", recent_predictions, timestamp, "database"
            ))
            
        except Exception as e:
            self.logger.warning(f"收集数据库指标失败: {e}")
        
        return metrics
    
    def _collect_fusion_metrics(self, timestamp: datetime) -> List[PerformanceMetric]:
        """收集融合系统指标"""
        metrics = []
        
        try:
            # 从数据库获取最近的性能数据
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 最近7天的命中率
                cursor.execute('''
                    SELECT AVG(CASE WHEN hit_type = 'exact' THEN 1.0 ELSE 0.0 END) as hit_rate
                    FROM prediction_performance
                    WHERE evaluated_at >= datetime('now', '-7 days')
                ''')
                
                result = cursor.fetchone()
                hit_rate = result[0] if result and result[0] is not None else 0.0
                
                metrics.append(PerformanceMetric(
                    "hit_rate", hit_rate, timestamp, "fusion", 0.1
                ))
                
                # Top-10命中率
                cursor.execute('''
                    SELECT AVG(CASE WHEN top_k_hit = 1 THEN 1.0 ELSE 0.0 END) as top10_hit_rate
                    FROM prediction_performance
                    WHERE evaluated_at >= datetime('now', '-7 days')
                ''')
                
                result = cursor.fetchone()
                top10_hit_rate = result[0] if result and result[0] is not None else 0.0
                
                metrics.append(PerformanceMetric(
                    "top10_hit_rate", top10_hit_rate, timestamp, "fusion", 0.3
                ))
                
                # 平均综合评分
                cursor.execute('''
                    SELECT AVG(overall_score) as avg_score
                    FROM prediction_performance
                    WHERE evaluated_at >= datetime('now', '-7 days')
                ''')
                
                result = cursor.fetchone()
                avg_score = result[0] if result and result[0] is not None else 0.0
                
                metrics.append(PerformanceMetric(
                    "avg_overall_score", avg_score, timestamp, "fusion"
                ))
                
        except Exception as e:
            self.logger.warning(f"收集融合指标失败: {e}")
        
        return metrics
    
    def _test_database_connection(self) -> float:
        """测试数据库连接时间"""
        start_time = time.time()
        
        try:
            with sqlite3.connect(self.db_path, timeout=5) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            return time.time() - start_time
            
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {e}")
            return 999.0  # 表示连接失败
    
    def _get_database_size(self) -> float:
        """获取数据库大小（MB）"""
        try:
            import os
            size_bytes = os.path.getsize(self.db_path)
            return size_bytes / (1024 * 1024)  # 转换为MB
        except Exception:
            return 0.0
    
    def _count_recent_predictions(self) -> int:
        """统计最近的预测记录数"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT COUNT(*) FROM final_predictions
                    WHERE created_at >= datetime('now', '-1 day')
                ''')
                
                result = cursor.fetchone()
                return result[0] if result else 0
                
        except Exception:
            return 0
    
    def _store_metric(self, metric: PerformanceMetric):
        """存储性能指标"""
        # 添加到缓冲区
        self.metrics_buffer.append(metric)
        
        # 添加到历史记录
        self.metrics_history[metric.name].append(metric)
        
        # 限制历史记录长度
        if len(self.metrics_history[metric.name]) > 1000:
            self.metrics_history[metric.name] = self.metrics_history[metric.name][-1000:]
    
    def _check_alerts(self, metrics: List[PerformanceMetric]):
        """检查告警"""
        for metric in metrics:
            for rule in self.alert_rules:
                if not rule.enabled or rule.metric_name != metric.name:
                    continue
                
                if self._evaluate_alert_condition(metric.value, rule):
                    self._trigger_alert(metric, rule)
    
    def _evaluate_alert_condition(self, value: float, rule: AlertRule) -> bool:
        """评估告警条件"""
        try:
            if rule.condition == ">":
                return value > rule.threshold
            elif rule.condition == "<":
                return value < rule.threshold
            elif rule.condition == ">=":
                return value >= rule.threshold
            elif rule.condition == "<=":
                return value <= rule.threshold
            elif rule.condition == "==":
                return abs(value - rule.threshold) < 1e-6
            elif rule.condition == "!=":
                return abs(value - rule.threshold) >= 1e-6
            else:
                return False
        except Exception:
            return False
    
    def _trigger_alert(self, metric: PerformanceMetric, rule: AlertRule):
        """触发告警"""
        alert_data = {
            'rule_name': rule.name,
            'metric_name': metric.name,
            'metric_value': metric.value,
            'threshold': rule.threshold,
            'severity': rule.severity,
            'timestamp': metric.timestamp.isoformat(),
            'category': metric.category
        }
        
        self.logger.warning(f"告警触发: {rule.name}, 指标值: {metric.value}, 阈值: {rule.threshold}")
        
        # 调用告警回调
        for callback in self.alert_callbacks:
            try:
                callback(alert_data)
            except Exception as e:
                self.logger.error(f"告警回调执行失败: {e}")
    
    def add_alert_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    def record_prediction(self, execution_time: float, success: bool):
        """记录预测执行"""
        self.stats['total_predictions'] += 1
        self.stats['total_execution_time'] += execution_time
        
        if success:
            self.stats['successful_predictions'] += 1
        else:
            self.stats['failed_predictions'] += 1
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        current_metrics = {}
        
        for metric in list(self.metrics_buffer)[-50:]:  # 最近50个指标
            current_metrics[metric.name] = {
                'value': metric.value,
                'timestamp': metric.timestamp.isoformat(),
                'category': metric.category,
                'threshold': metric.threshold,
                'status': metric.status
            }
        
        return current_metrics
    
    def get_metrics_history(self, metric_name: str, hours: int = 24) -> List[Dict[str, Any]]:
        """获取指标历史"""
        if metric_name not in self.metrics_history:
            return []
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        history = []
        
        for metric in self.metrics_history[metric_name]:
            if metric.timestamp >= cutoff_time:
                history.append({
                    'value': metric.value,
                    'timestamp': metric.timestamp.isoformat(),
                    'status': metric.status
                })
        
        return history
    
    def get_monitoring_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        return {
            'monitoring_status': self.is_monitoring,
            'total_metrics': len(self.metrics_buffer),
            'alert_rules_count': len(self.alert_rules),
            'active_alerts': len([r for r in self.alert_rules if r.enabled]),
            'stats': self.stats.copy(),
            'uptime_hours': (datetime.now() - self.stats['start_time']).total_seconds() / 3600
        }
