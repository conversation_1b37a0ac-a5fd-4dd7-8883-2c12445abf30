#!/usr/bin/env python3
"""
P9系统基础测试

该测试文件验证P9系统的基本功能，包括：
1. 组件导入测试
2. 基本初始化测试
3. 系统状态检查测试

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import unittest
import tempfile
import os
import logging
from pathlib import Path

# 设置测试日志
logging.basicConfig(level=logging.INFO)

class TestP9Basic(unittest.TestCase):
    """P9系统基础测试类"""
    
    def setUp(self):
        """测试初始化"""
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db_path = self.temp_db.name
        self.temp_db.close()
        
        # 创建临时配置目录
        self.temp_config_dir = tempfile.mkdtemp()
        self.test_config_path = self.temp_config_dir
        
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def tearDown(self):
        """测试清理"""
        try:
            if os.path.exists(self.test_db_path):
                os.unlink(self.test_db_path)
            
            import shutil
            if os.path.exists(self.test_config_path):
                shutil.rmtree(self.test_config_path)
                
        except Exception as e:
            self.logger.error(f"测试清理失败: {e}")
    
    def test_p9_component_imports(self):
        """测试P9组件导入"""
        try:
            # 测试核心组件导入
            from src.optimization.intelligent_closed_loop_optimizer import IntelligentClosedLoopOptimizer
            from src.optimization.intelligent_optimization_manager import IntelligentOptimizationManager
            
            self.assertIsNotNone(IntelligentClosedLoopOptimizer)
            self.assertIsNotNone(IntelligentOptimizationManager)
            
            self.logger.info("✅ P9组件导入测试通过")
            
        except ImportError as e:
            self.fail(f"P9组件导入失败: {e}")
    
    def test_p9_optimizer_initialization(self):
        """测试P9优化器初始化"""
        try:
            from src.optimization.intelligent_closed_loop_optimizer import IntelligentClosedLoopOptimizer
            
            optimizer = IntelligentClosedLoopOptimizer(self.test_db_path, self.test_config_path)
            
            # 验证初始化状态
            self.assertIsNotNone(optimizer)
            self.assertFalse(optimizer.is_running)
            self.assertIsNotNone(optimizer.optimization_config)
            self.assertIsNotNone(optimizer.performance_thresholds)
            
            self.logger.info("✅ P9优化器初始化测试通过")
            
        except Exception as e:
            self.fail(f"P9优化器初始化测试失败: {e}")
    
    def test_p9_manager_initialization(self):
        """测试P9管理器初始化"""
        try:
            from src.optimization.intelligent_optimization_manager import IntelligentOptimizationManager
            
            manager = IntelligentOptimizationManager(self.test_db_path, self.test_config_path)
            
            # 验证初始化状态
            self.assertIsNotNone(manager)
            self.assertIsNotNone(manager.optimizer)
            
            self.logger.info("✅ P9管理器初始化测试通过")
            
        except Exception as e:
            self.fail(f"P9管理器初始化测试失败: {e}")
    
    def test_p9_system_status(self):
        """测试P9系统状态获取"""
        try:
            from src.optimization.intelligent_optimization_manager import IntelligentOptimizationManager
            
            manager = IntelligentOptimizationManager(self.test_db_path, self.test_config_path)
            
            # 获取系统状态
            status = manager.get_system_status()
            
            # 验证状态结构
            self.assertIn('is_running', status)
            self.assertIn('optimization_config', status)
            self.assertIn('performance_thresholds', status)
            self.assertIn('p8_system_status', status)
            self.assertIn('system_health', status)
            
            self.logger.info("✅ P9系统状态测试通过")
            
        except Exception as e:
            self.fail(f"P9系统状态测试失败: {e}")
    
    def test_p9_system_lifecycle(self):
        """测试P9系统生命周期"""
        try:
            from src.optimization.intelligent_optimization_manager import IntelligentOptimizationManager
            
            manager = IntelligentOptimizationManager(self.test_db_path, self.test_config_path)
            
            # 测试系统启动
            start_result = manager.start_system()
            self.assertEqual(start_result['status'], 'success')
            
            # 测试系统状态
            status = manager.get_system_status()
            self.assertTrue(status.get('is_running', False))
            
            # 测试系统停止
            stop_result = manager.stop_system()
            self.assertEqual(stop_result['status'], 'success')
            
            self.logger.info("✅ P9系统生命周期测试通过")
            
        except Exception as e:
            self.fail(f"P9系统生命周期测试失败: {e}")

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
