#!/usr/bin/env python3
"""
分布预测和值模型（专属特征）

实现基于分布预测的和值模型
预测和值概率分布，计算分布熵值和期望值

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
import pickle
import logging
from typing import Dict, List, Tuple, Optional, Any
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, log_loss
import sqlite3
import json
from pathlib import Path
from scipy import stats

class DistributionSumModel:
    """分布预测和值模型（专属特征）"""
    
    def __init__(self, db_path: str, config: Optional[Dict] = None):
        """
        初始化分布预测和值模型
        
        Args:
            db_path: 数据库路径
            config: 模型配置参数
        """
        self.db_path = db_path
        self.model = None
        self.is_trained = False
        self.feature_names = None
        self.logger = logging.getLogger("DistributionSumModel")
        
        # 和值范围：0-27，共28个类别
        self.num_classes = 28
        self.sum_range = list(range(28))
        
        # 默认模型参数
        self.model_params = {
            'objective': 'multi:softprob',
            'num_class': 28,
            'max_depth': 6,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'n_estimators': 200,
            'random_state': 42,
            'n_jobs': -1
        }
        
        # 更新配置参数
        if config and 'distribution' in config:
            self.model_params.update(config['distribution'])
    
    def build_model(self):
        """构建XGBoost分类模型"""
        try:
            import xgboost as xgb
            self.model = xgb.XGBClassifier(**self.model_params)
            self.logger.info("分布预测和值模型构建成功")
            return self.model
        except ImportError:
            error_msg = "XGBoost未安装，请安装: pip install xgboost"
            self.logger.error(error_msg)
            raise ImportError(error_msg)
    
    def prepare_features(self, data: pd.DataFrame, window_size: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备训练特征
        
        Args:
            data: 包含彩票数据的DataFrame
            window_size: 滑动窗口大小
            
        Returns:
            特征矩阵X和目标向量y
        """
        # 计算和值
        data['sum_value'] = data['hundreds'] + data['tens'] + data['units']
        
        X = []
        y = []
        
        # 构建滑动窗口特征
        for i in range(window_size, len(data)):
            features = []
            
            # 历史和值特征
            recent_sums = data.iloc[i - window_size:i]['sum_value']
            
            # 基础统计特征
            features.extend([
                recent_sums.mean(),      # 平均值
                recent_sums.std(),       # 标准差
                recent_sums.min(),       # 最小值
                recent_sums.max(),       # 最大值
                recent_sums.median(),    # 中位数
            ])
            
            # 分布特征（专属）
            sum_counts = recent_sums.value_counts()
            
            # 和值分布熵
            if len(sum_counts) > 1:
                probabilities = sum_counts.values / len(recent_sums)
                entropy = stats.entropy(probabilities)
                features.append(entropy)
            else:
                features.append(0)
            
            # 和值分布偏度和峰度
            features.extend([
                recent_sums.skew(),      # 偏度
                recent_sums.kurtosis()   # 峰度
            ])
            
            # 和值频次特征
            for sum_val in range(28):
                count = (recent_sums == sum_val).sum()
                features.append(count / window_size)  # 归一化频次
            
            # 和值趋势特征
            if len(recent_sums) >= 3:
                x_vals = np.arange(len(recent_sums))
                trend = np.polyfit(x_vals, recent_sums, 1)[0]
                features.append(trend)
            else:
                features.append(0)
            
            # 和值变化特征
            sum_diff = recent_sums.diff().dropna()
            if len(sum_diff) > 0:
                features.extend([
                    sum_diff.mean(),     # 平均变化
                    sum_diff.std(),      # 变化标准差
                    (sum_diff > 0).sum() / len(sum_diff)  # 上升比例
                ])
            else:
                features.extend([0, 0, 0])
            
            X.append(features)
            y.append(data.iloc[i]['sum_value'])
        
        # 设置特征名称
        self.feature_names = [
            'sum_mean', 'sum_std', 'sum_min', 'sum_max', 'sum_median',
            'sum_entropy', 'sum_skew', 'sum_kurtosis'
        ]
        
        # 添加频次特征名称
        for i in range(28):
            self.feature_names.append(f'sum_{i}_freq')
        
        # 添加其他特征名称
        self.feature_names.extend([
            'sum_trend', 'sum_diff_mean', 'sum_diff_std', 'sum_up_ratio'
        ])
        
        return np.array(X), np.array(y)
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """从数据库加载训练数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 查询彩票数据
            query = """
                SELECT hundreds, tens, units, issue, draw_date
                FROM lottery_data
                ORDER BY draw_date, issue
            """
            
            data = pd.read_sql_query(query, conn)
            conn.close()
            
            if data.empty:
                raise ValueError("没有找到彩票数据")
            
            self.logger.info(f"加载了 {len(data)} 条彩票数据")
            
            # 准备特征
            X, y = self.prepare_features(data)
            
            self.logger.info(f"生成了 {len(X)} 个训练样本，特征维度: {X.shape[1]}")
            
            return X, y
            
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            raise
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        训练分布预测模型
        
        Args:
            X: 特征矩阵，如果为None则从数据库加载
            y: 目标向量，如果为None则从数据库加载
            
        Returns:
            训练性能指标
        """
        try:
            # 如果没有提供数据，从数据库加载
            if X is None or y is None:
                X, y = self.load_data()
            
            # 构建模型
            if self.model is None:
                self.build_model()
            
            # 分割训练集和验证集
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            self.logger.info(f"训练集大小: {len(X_train)}, 验证集大小: {len(X_val)}")
            
            # 训练模型
            self.model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                early_stopping_rounds=20,
                verbose=False
            )
            
            self.is_trained = True
            
            # 评估模型
            performance = self.evaluate(X_val, y_val)
            
            self.logger.info(f"分布预测和值模型训练完成，验证集性能: {performance}")
            
            return performance
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            raise
    
    def predict_distribution(self, X: np.ndarray) -> np.ndarray:
        """
        预测和值概率分布
        
        Args:
            X: 特征矩阵
            
        Returns:
            和值概率分布矩阵 (n_samples, 28)
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 预测概率分布
            probabilities = self.model.predict_proba(X)
            return probabilities
            
        except Exception as e:
            self.logger.error(f"预测分布失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        预测和值（期望值）
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测的和值数组
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 获取概率分布
            probabilities = self.predict_distribution(X)
            
            # 计算期望值
            expected_values = []
            for prob_dist in probabilities:
                expected_value = np.sum(prob_dist * np.arange(28))
                expected_values.append(expected_value)
            
            return np.array(expected_values)
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测和值及置信度
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测值和置信度数组
        """
        probabilities = self.predict_distribution(X)
        predictions = self.predict(X)
        
        # 基于分布熵计算置信度
        confidences = []
        for prob_dist in probabilities:
            # 计算分布熵，熵越小置信度越高
            entropy = stats.entropy(prob_dist + 1e-10)  # 避免log(0)
            max_entropy = np.log(28)  # 最大熵（均匀分布）
            confidence = 1.0 - (entropy / max_entropy)
            confidence = max(0.1, min(0.9, confidence))
            confidences.append(confidence)
        
        return predictions, np.array(confidences)
    
    def calculate_distribution_entropy(self, X: np.ndarray) -> np.ndarray:
        """
        计算预测分布的熵值
        
        Args:
            X: 特征矩阵
            
        Returns:
            分布熵值数组
        """
        probabilities = self.predict_distribution(X)
        
        entropies = []
        for prob_dist in probabilities:
            entropy = stats.entropy(prob_dist + 1e-10)
            entropies.append(entropy)
        
        return np.array(entropies)
    
    def get_top_k_predictions(self, X: np.ndarray, k: int = 3) -> List[List[Tuple[int, float]]]:
        """
        获取Top-K预测结果
        
        Args:
            X: 特征矩阵
            k: 返回前k个最可能的和值
            
        Returns:
            每个样本的Top-K预测列表 [(和值, 概率), ...]
        """
        probabilities = self.predict_distribution(X)
        
        results = []
        for prob_dist in probabilities:
            # 获取Top-K索引
            top_k_indices = np.argsort(prob_dist)[-k:][::-1]
            top_k_results = [(idx, prob_dist[idx]) for idx in top_k_indices]
            results.append(top_k_results)
        
        return results
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            X_test: 测试特征
            y_test: 测试目标
            
        Returns:
            性能指标字典
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 预测概率分布和期望值
            probabilities = self.predict_distribution(X_test)
            y_pred = self.predict(X_test)
            
            # 分类准确率
            y_pred_class = np.argmax(probabilities, axis=1)
            accuracy = accuracy_score(y_test, y_pred_class)
            
            # 对数损失
            logloss = log_loss(y_test, probabilities)
            
            # 期望值的回归指标
            mae = np.mean(np.abs(y_test - y_pred))
            rmse = np.sqrt(np.mean((y_test - y_pred) ** 2))
            
            # Top-K准确率
            top_3_accuracy = 0
            for i, true_val in enumerate(y_test):
                top_3_indices = np.argsort(probabilities[i])[-3:]
                if true_val in top_3_indices:
                    top_3_accuracy += 1
            top_3_accuracy /= len(y_test)
            
            # 分布质量指标
            avg_entropy = np.mean([stats.entropy(prob + 1e-10) for prob in probabilities])
            
            return {
                'accuracy': accuracy,
                'log_loss': logloss,
                'mae': mae,
                'rmse': rmse,
                'top_3_accuracy': top_3_accuracy,
                'avg_entropy': avg_entropy,
                'distribution_accuracy': accuracy  # 专属指标
            }
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            raise
    
    def save_model(self, filepath: str) -> bool:
        """保存模型"""
        if not self.is_trained:
            self.logger.warning("模型尚未训练，无法保存")
            return False
        
        try:
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            model_data = {
                'model': self.model,
                'model_params': self.model_params,
                'feature_names': self.feature_names,
                'is_trained': self.is_trained,
                'num_classes': self.num_classes,
                'sum_range': self.sum_range
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"模型保存成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """加载模型"""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.model_params = model_data['model_params']
            self.feature_names = model_data['feature_names']
            self.is_trained = model_data['is_trained']
            self.num_classes = model_data['num_classes']
            self.sum_range = model_data['sum_range']
            
            self.logger.info(f"模型加载成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False
