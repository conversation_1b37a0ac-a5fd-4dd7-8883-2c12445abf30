#!/usr/bin/env python3
"""
融合数据访问层

管理融合系统的数据存储和检索
为P8智能交集融合系统提供数据访问接口

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sqlite3
import json
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

@dataclass
class FinalPrediction:
    """最终预测结果数据类"""
    issue: str
    prediction_rank: int
    hundreds: int
    tens: int
    units: int
    sum_value: int
    span_value: int
    combined_probability: float
    hundreds_prob: float
    tens_prob: float
    units_prob: float
    sum_prob: float
    span_prob: float
    sum_consistency: float
    span_consistency: float
    constraint_score: float
    diversity_score: float
    confidence_level: str
    fusion_method: str
    ranking_strategy: str
    created_at: str

class FusionDataAccess:
    """融合数据访问层"""
    
    def __init__(self, db_path: str):
        """
        初始化融合数据访问层
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # 确保数据库连接正常
        self._verify_database()
    
    def _verify_database(self):
        """验证数据库连接和表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查关键表是否存在
                required_tables = [
                    'final_predictions',
                    'fusion_weights',
                    'prediction_performance',
                    'fusion_constraint_rules',
                    'fusion_sessions'
                ]
                
                for table in required_tables:
                    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                    if not cursor.fetchone():
                        self.logger.warning(f"表 {table} 不存在，可能需要运行数据库初始化脚本")
                
        except Exception as e:
            self.logger.error(f"数据库验证失败: {e}")
    
    # ==================== 最终预测结果管理 ====================
    
    def save_final_predictions(self, predictions: List[Dict[str, Any]], issue: str) -> bool:
        """
        保存最终预测结果
        
        Args:
            predictions: 预测结果列表
            issue: 期号
            
        Returns:
            是否保存成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 删除该期号的旧预测
                cursor.execute("DELETE FROM final_predictions WHERE issue = ?", (issue,))
                
                # 插入新预测
                for pred in predictions:
                    cursor.execute('''
                        INSERT INTO final_predictions (
                            issue, prediction_rank, hundreds, tens, units,
                            sum_value, span_value, combined_probability,
                            hundreds_prob, tens_prob, units_prob,
                            sum_prob, span_prob, sum_consistency,
                            span_consistency, constraint_score, diversity_score,
                            confidence_level, fusion_method, ranking_strategy
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        issue, pred['rank'], pred['hundreds'], pred['tens'], pred['units'],
                        pred['sum_value'], pred['span_value'], pred['combined_probability'],
                        pred['hundreds_prob'], pred['tens_prob'], pred['units_prob'],
                        pred['sum_prob'], pred['span_prob'], pred['sum_consistency'],
                        pred['span_consistency'], pred['constraint_score'], pred['diversity_score'],
                        pred['confidence_level'], pred['fusion_method'], pred['ranking_strategy']
                    ))
                
                conn.commit()
                self.logger.info(f"保存了 {len(predictions)} 个预测结果，期号: {issue}")
                return True
                
        except Exception as e:
            self.logger.error(f"保存最终预测结果失败: {e}")
            return False
    
    def get_final_predictions(self, issue: str, top_k: Optional[int] = None) -> List[FinalPrediction]:
        """
        获取最终预测结果
        
        Args:
            issue: 期号
            top_k: 返回前K个结果
            
        Returns:
            预测结果列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                sql = '''
                    SELECT * FROM final_predictions 
                    WHERE issue = ? 
                    ORDER BY prediction_rank
                '''
                
                if top_k:
                    sql += f" LIMIT {top_k}"
                
                cursor.execute(sql, (issue,))
                rows = cursor.fetchall()
                
                # 获取列名
                columns = [description[0] for description in cursor.description]
                
                # 转换为数据类
                predictions = []
                for row in rows:
                    row_dict = dict(zip(columns, row))
                    prediction = FinalPrediction(**row_dict)
                    predictions.append(prediction)
                
                return predictions
                
        except Exception as e:
            self.logger.error(f"获取最终预测结果失败: {e}")
            return []
    
    def get_latest_predictions(self, top_k: int = 10) -> List[FinalPrediction]:
        """获取最新的预测结果"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取最新期号
                cursor.execute("SELECT MAX(issue) FROM final_predictions")
                latest_issue = cursor.fetchone()[0]
                
                if latest_issue:
                    return self.get_final_predictions(latest_issue, top_k)
                else:
                    return []
                    
        except Exception as e:
            self.logger.error(f"获取最新预测结果失败: {e}")
            return []
    
    # ==================== 权重管理 ====================
    
    def save_fusion_weights(self, weights: Dict[str, float], performance_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        保存融合权重
        
        Args:
            weights: 权重字典
            performance_data: 性能数据
            
        Returns:
            是否保存成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for name, weight in weights.items():
                    # 确定权重类型
                    if name in ['hundreds', 'tens', 'units']:
                        weight_type = 'position'
                    elif name in ['sum', 'span']:
                        weight_type = 'auxiliary'
                    else:
                        weight_type = 'constraint'
                    
                    # 获取性能数据
                    perf_data = performance_data.get(name, {}) if performance_data else {}
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO fusion_weights (
                            weight_type, predictor_name, model_name, weight_value,
                            performance_score, accuracy_rate, confidence_score, last_updated
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        weight_type, name, 'ensemble', weight,
                        perf_data.get('performance_score', 0.5),
                        perf_data.get('accuracy_rate', 0.5),
                        perf_data.get('confidence_score', 0.5),
                        datetime.now().isoformat()
                    ))
                
                conn.commit()
                self.logger.info(f"保存了 {len(weights)} 个权重配置")
                return True
                
        except Exception as e:
            self.logger.error(f"保存融合权重失败: {e}")
            return False
    
    def get_fusion_weights(self, active_only: bool = True) -> Dict[str, float]:
        """
        获取融合权重
        
        Args:
            active_only: 是否只获取激活的权重
            
        Returns:
            权重字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                sql = "SELECT predictor_name, weight_value FROM fusion_weights"
                if active_only:
                    sql += " WHERE is_active = 1"
                
                cursor.execute(sql)
                rows = cursor.fetchall()
                
                weights = {}
                for predictor_name, weight_value in rows:
                    weights[predictor_name] = weight_value
                
                return weights
                
        except Exception as e:
            self.logger.error(f"获取融合权重失败: {e}")
            return {}
    
    # ==================== 性能评估管理 ====================
    
    def save_performance_evaluation(self, issue: str, actual_result: str, 
                                   predicted_results: List[Dict[str, Any]]) -> bool:
        """
        保存性能评估结果
        
        Args:
            issue: 期号
            actual_result: 实际结果 (如 "123")
            predicted_results: 预测结果列表
            
        Returns:
            是否保存成功
        """
        try:
            if len(actual_result) != 3:
                raise ValueError("实际结果格式错误")
            
            actual_h, actual_t, actual_u = int(actual_result[0]), int(actual_result[1]), int(actual_result[2])
            actual_sum = actual_h + actual_t + actual_u
            actual_span = max(actual_h, actual_t, actual_u) - min(actual_h, actual_t, actual_u)
            
            # 查找实际结果在预测中的排名
            predicted_rank = None
            hit_type = 'none'
            
            for i, pred in enumerate(predicted_results):
                if (pred['hundreds'] == actual_h and 
                    pred['tens'] == actual_t and 
                    pred['units'] == actual_u):
                    predicted_rank = i + 1
                    hit_type = 'exact'
                    break
                elif (pred['hundreds'] == actual_h or 
                      pred['tens'] == actual_t or 
                      pred['units'] == actual_u):
                    if predicted_rank is None:
                        predicted_rank = i + 1
                        hit_type = 'position'
            
            # 计算各种准确性
            hundreds_accuracy = any(p['hundreds'] == actual_h for p in predicted_results[:10])
            tens_accuracy = any(p['tens'] == actual_t for p in predicted_results[:10])
            units_accuracy = any(p['units'] == actual_u for p in predicted_results[:10])
            sum_accuracy = any(abs(p['sum_value'] - actual_sum) <= 2 for p in predicted_results[:10])
            span_accuracy = any(abs(p['span_value'] - actual_span) <= 1 for p in predicted_results[:10])
            
            # 计算评分
            overall_score = self._calculate_overall_score(hit_type, predicted_rank, len(predicted_results))
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO prediction_performance (
                        issue, actual_hundreds, actual_tens, actual_units,
                        actual_sum, actual_span, predicted_rank, hit_type,
                        hundreds_accuracy, tens_accuracy, units_accuracy,
                        sum_accuracy, span_accuracy, overall_score,
                        fusion_method, ranking_strategy, top_k_hit
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    issue, actual_h, actual_t, actual_u, actual_sum, actual_span,
                    predicted_rank, hit_type, hundreds_accuracy, tens_accuracy,
                    units_accuracy, sum_accuracy, span_accuracy, overall_score,
                    predicted_results[0].get('fusion_method', 'unknown'),
                    predicted_results[0].get('ranking_strategy', 'unknown'),
                    1 if predicted_rank and predicted_rank <= 10 else 0
                ))
                
                conn.commit()
                self.logger.info(f"保存性能评估结果，期号: {issue}, 命中类型: {hit_type}")
                return True
                
        except Exception as e:
            self.logger.error(f"保存性能评估失败: {e}")
            return False
    
    def _calculate_overall_score(self, hit_type: str, predicted_rank: Optional[int], total_predictions: int) -> float:
        """计算综合评分"""
        if hit_type == 'exact':
            if predicted_rank == 1:
                return 1.0
            elif predicted_rank <= 5:
                return 0.8
            elif predicted_rank <= 10:
                return 0.6
            else:
                return 0.4
        elif hit_type == 'position':
            return 0.3
        else:
            return 0.0
    
    def get_performance_summary(self, days: int = 30) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Args:
            days: 统计天数
            
        Returns:
            性能摘要字典
        """
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_evaluations,
                        SUM(CASE WHEN hit_type = 'exact' THEN 1 ELSE 0 END) as exact_hits,
                        SUM(CASE WHEN hit_type IN ('exact', 'position') THEN 1 ELSE 0 END) as position_hits,
                        SUM(CASE WHEN top_k_hit = 1 THEN 1 ELSE 0 END) as top_10_hits,
                        AVG(overall_score) as avg_overall_score,
                        AVG(CASE WHEN hundreds_accuracy THEN 1.0 ELSE 0.0 END) as hundreds_accuracy_rate,
                        AVG(CASE WHEN tens_accuracy THEN 1.0 ELSE 0.0 END) as tens_accuracy_rate,
                        AVG(CASE WHEN units_accuracy THEN 1.0 ELSE 0.0 END) as units_accuracy_rate
                    FROM prediction_performance
                    WHERE evaluated_at >= ?
                ''', (cutoff_date,))
                
                row = cursor.fetchone()
                
                if row and row[0] > 0:  # 有数据
                    summary = {
                        'total_evaluations': row[0],
                        'exact_hits': row[1],
                        'position_hits': row[2],
                        'top_10_hits': row[3],
                        'exact_hit_rate': row[1] / row[0] if row[0] > 0 else 0,
                        'position_hit_rate': row[2] / row[0] if row[0] > 0 else 0,
                        'top_10_hit_rate': row[3] / row[0] if row[0] > 0 else 0,
                        'avg_overall_score': row[4] or 0,
                        'hundreds_accuracy_rate': row[5] or 0,
                        'tens_accuracy_rate': row[6] or 0,
                        'units_accuracy_rate': row[7] or 0,
                        'evaluation_period_days': days
                    }
                else:
                    summary = {
                        'total_evaluations': 0,
                        'exact_hits': 0,
                        'position_hits': 0,
                        'top_10_hits': 0,
                        'exact_hit_rate': 0,
                        'position_hit_rate': 0,
                        'top_10_hit_rate': 0,
                        'avg_overall_score': 0,
                        'hundreds_accuracy_rate': 0,
                        'tens_accuracy_rate': 0,
                        'units_accuracy_rate': 0,
                        'evaluation_period_days': days
                    }
                
                return summary
                
        except Exception as e:
            self.logger.error(f"获取性能摘要失败: {e}")
            return {}
    
    # ==================== 融合会话管理 ====================
    
    def save_fusion_session(self, session_id: str, issue: str, fusion_method: str,
                           ranking_strategy: str, input_data: Dict[str, Any],
                           output_data: Dict[str, Any], execution_time: float,
                           success: bool = True, error_message: str = None) -> bool:
        """保存融合会话记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO fusion_sessions (
                        session_id, issue, fusion_method, ranking_strategy,
                        input_data, output_data, execution_time, success, error_message
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    session_id, issue, fusion_method, ranking_strategy,
                    json.dumps(input_data), json.dumps(output_data),
                    execution_time, success, error_message
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"保存融合会话失败: {e}")
            return False
    
    def get_recent_sessions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的融合会话"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT session_id, issue, fusion_method, ranking_strategy,
                           execution_time, success, created_at
                    FROM fusion_sessions
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (limit,))
                
                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                
                sessions = []
                for row in rows:
                    session = dict(zip(columns, row))
                    sessions.append(session)
                
                return sessions
                
        except Exception as e:
            self.logger.error(f"获取最近会话失败: {e}")
            return []
