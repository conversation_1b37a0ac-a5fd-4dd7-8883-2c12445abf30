#!/usr/bin/env python3
"""
统一预测器接口管理器

管理所有预测器的加载和调用，提供标准化的数据接口
为P8智能交集融合系统提供统一的预测器访问接口

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from pathlib import Path
import json
from datetime import datetime

class UnifiedPredictorInterface:
    """统一预测器接口管理器"""
    
    def __init__(self, db_path: str, config_path: Optional[str] = None):
        """
        初始化统一预测器接口
        
        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        self.db_path = db_path
        self.config_path = config_path
        self.predictors = {}
        self.logger = logging.getLogger(__name__)
        
        # 预测器配置
        self.predictor_config = {
            'hundreds': {
                'class_name': 'HundredsPredictor',
                'module_path': 'src.predictors.hundreds_predictor',
                'position': 'hundreds',
                'output_size': 10
            },
            'tens': {
                'class_name': 'TensPredictor', 
                'module_path': 'src.predictors.tens_predictor',
                'position': 'tens',
                'output_size': 10
            },
            'units': {
                'class_name': 'UnitsPredictor',
                'module_path': 'src.predictors.units_predictor', 
                'position': 'units',
                'output_size': 10
            },
            'sum': {
                'class_name': 'SumPredictor',
                'module_path': 'src.predictors.sum_predictor',
                'position': 'sum',
                'output_size': 28
            },
            'span': {
                'class_name': 'SpanPredictor',
                'module_path': 'src.predictors.span_predictor',
                'position': 'span', 
                'output_size': 10
            }
        }
        
        self.logger.info("统一预测器接口管理器初始化完成")
    
    def load_all_predictors(self) -> bool:
        """
        加载所有预测器
        
        Returns:
            是否成功加载所有预测器
        """
        try:
            success_count = 0
            total_count = len(self.predictor_config)
            
            for name, config in self.predictor_config.items():
                try:
                    # 动态导入预测器类
                    module = __import__(config['module_path'], fromlist=[config['class_name']])
                    predictor_class = getattr(module, config['class_name'])
                    
                    # 实例化预测器
                    predictor = predictor_class(self.db_path)
                    self.predictors[name] = predictor
                    
                    self.logger.info(f"成功加载预测器: {name}")
                    success_count += 1
                    
                except Exception as e:
                    self.logger.error(f"加载预测器 {name} 失败: {e}")
                    self.predictors[name] = None
            
            if success_count == total_count:
                self.logger.info("所有预测器加载完成")
                return True
            else:
                self.logger.warning(f"部分预测器加载失败: {success_count}/{total_count}")
                return False
                
        except Exception as e:
            self.logger.error(f"加载预测器失败: {e}")
            return False
    
    def get_all_predictions(self, issue: str) -> Dict[str, Any]:
        """
        获取所有预测器的标准化预测结果
        
        Args:
            issue: 期号
            
        Returns:
            标准化预测结果字典
        """
        predictions = {}
        
        for name, predictor in self.predictors.items():
            if predictor is None:
                predictions[name] = {'error': 'predictor_not_loaded'}
                continue
                
            try:
                if name in ['hundreds', 'tens', 'units']:
                    # 位置预测器
                    result = predictor.predict_next_period(issue)
                    predictions[name] = {
                        'probabilities': result.get('probabilities', []),
                        'predicted_digit': result.get('predicted_digit', 0),
                        'confidence': result.get('confidence', 0.5),
                        'prediction_type': 'position',
                        'output_size': 10
                    }
                    
                elif name == 'sum':
                    # 和值预测器
                    features = predictor._build_features_for_prediction()
                    if features is not None and len(features) > 0:
                        sum_pred = predictor.predict(features)[0]
                        sum_probs = predictor.predict_probability(features)[0]
                        constraint_info = predictor.get_constraint_info(features)
                        
                        predictions[name] = {
                            'predicted_sum': float(sum_pred),
                            'probabilities': sum_probs.tolist(),
                            'confidence': float(np.max(sum_probs)),
                            'constraint_info': constraint_info,
                            'prediction_type': 'sum',
                            'output_size': 28
                        }
                    else:
                        predictions[name] = {'error': 'no_features_available'}
                        
                elif name == 'span':
                    # 跨度预测器
                    result = predictor.predict_next_period(issue)
                    predictions[name] = {
                        'predicted_span': result.get('optimized_prediction', result.get('predicted_span', 0)),
                        'probabilities': result.get('probabilities', []),
                        'confidence': result.get('confidence', 0.5),
                        'constraint_info': result.get('constraint_info', {}),
                        'prediction_type': 'span',
                        'output_size': 10
                    }
                    
            except Exception as e:
                self.logger.error(f"{name}预测器预测失败: {e}")
                predictions[name] = {'error': str(e)}
        
        return predictions
    
    def get_constraint_matrix(self, issue: str) -> Dict[str, Any]:
        """
        获取约束矩阵
        
        Args:
            issue: 期号
            
        Returns:
            约束矩阵信息
        """
        try:
            predictions = self.get_all_predictions(issue)
            
            constraint_matrix = {
                'sum_constraints': predictions.get('sum', {}).get('constraint_info', {}),
                'span_constraints': predictions.get('span', {}).get('constraint_info', {}),
                'position_constraints': {
                    'hundreds': predictions.get('hundreds', {}),
                    'tens': predictions.get('tens', {}),
                    'units': predictions.get('units', {})
                },
                'issue': issue,
                'timestamp': datetime.now().isoformat()
            }
            
            return constraint_matrix
            
        except Exception as e:
            self.logger.error(f"获取约束矩阵失败: {e}")
            return {'error': str(e)}
    
    def validate_predictions(self, predictions: Dict[str, Any]) -> Dict[str, bool]:
        """
        验证预测结果的有效性
        
        Args:
            predictions: 预测结果
            
        Returns:
            验证结果
        """
        validation_results = {}
        
        for name, pred in predictions.items():
            if 'error' in pred:
                validation_results[name] = False
                continue
                
            try:
                if name in ['hundreds', 'tens', 'units']:
                    # 验证位置预测
                    valid = (
                        'probabilities' in pred and
                        len(pred['probabilities']) == 10 and
                        'predicted_digit' in pred and
                        0 <= pred['predicted_digit'] <= 9
                    )
                elif name == 'sum':
                    # 验证和值预测
                    valid = (
                        'predicted_sum' in pred and
                        0 <= pred['predicted_sum'] <= 27 and
                        'probabilities' in pred and
                        len(pred['probabilities']) == 28
                    )
                elif name == 'span':
                    # 验证跨度预测
                    valid = (
                        'predicted_span' in pred and
                        0 <= pred['predicted_span'] <= 9 and
                        'probabilities' in pred and
                        len(pred['probabilities']) == 10
                    )
                else:
                    valid = False
                    
                validation_results[name] = valid
                
            except Exception as e:
                self.logger.error(f"验证{name}预测失败: {e}")
                validation_results[name] = False
        
        return validation_results
    
    def get_predictor_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有预测器的状态
        
        Returns:
            预测器状态信息
        """
        status = {}
        
        for name, predictor in self.predictors.items():
            if predictor is None:
                status[name] = {
                    'loaded': False,
                    'trained': False,
                    'error': 'not_loaded'
                }
            else:
                try:
                    status[name] = {
                        'loaded': True,
                        'trained': predictor.is_trained,
                        'model_count': len(predictor.models) if hasattr(predictor, 'models') else 0,
                        'current_model': getattr(predictor, 'current_model', 'unknown')
                    }
                except Exception as e:
                    status[name] = {
                        'loaded': True,
                        'trained': False,
                        'error': str(e)
                    }
        
        return status
