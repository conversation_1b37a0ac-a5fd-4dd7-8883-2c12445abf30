# P7跨度预测器配置文件
# 创建日期: 2025-01-14
# 描述: P7跨度预测器的完整配置，包括模型参数、跨度专属配置、双重约束配置等

# 数据库配置
database:
  path: "data/lottery.db"
  backup_enabled: true
  backup_interval: 24  # 小时

# 跨度预测器配置
span_predictor:
  # 基础配置
  position: "span"
  prediction_range: [0, 9]  # 跨度范围0-9
  target_type: "regression"  # 主要是回归，但包含分类模型
  
  # 跨度专属配置
  span_config:
    enable_classification: true      # 启用分类预测
    enable_pattern_analysis: true    # 启用模式分析
    enable_dual_constraints: true    # 启用双重约束
    
    # 分类配置
    classification:
      num_classes: 10               # 10分类（0-9）
      top_k_predictions: 3          # Top-K预测
      probability_threshold: 0.1    # 概率阈值
    
    # 模式分析配置
    pattern_analysis:
      enable_ascending: true        # 升序模式
      enable_descending: true       # 降序模式
      enable_same_digit: true       # 相同数字模式
      enable_consecutive: true      # 连续数字模式
      pattern_weights:
        ascending: 0.25
        descending: 0.25
        same_digit: 0.25
        consecutive: 0.25
    
    # 双重约束配置
    dual_constraints:
      enable_position_constraint: true   # 位置约束
      enable_sum_constraint: true        # 和值约束
      position_weight: 0.4               # 位置约束权重
      sum_weight: 0.4                    # 和值约束权重
      span_weight: 0.2                   # 跨度自身权重
      
      # 约束一致性阈值
      consistency_thresholds:
        position: 0.7
        sum: 0.7
        dual: 0.8
  
  # 模型配置
  models:
    # XGBoost回归模型
    xgb:
      n_estimators: 200
      max_depth: 6
      learning_rate: 0.1
      subsample: 0.8
      colsample_bytree: 0.8
      random_state: 42
      n_jobs: -1
      early_stopping_rounds: 20
      eval_metric: "rmse"
      
    # LightGBM回归模型
    lgb:
      n_estimators: 200
      max_depth: 6
      learning_rate: 0.1
      subsample: 0.8
      colsample_bytree: 0.8
      random_state: 42
      n_jobs: -1
      early_stopping_rounds: 20
      metric: "rmse"
      verbose: -1
      
    # LSTM回归模型
    lstm:
      sequence_length: 10
      hidden_units: 64
      dropout_rate: 0.2
      epochs: 100
      batch_size: 32
      learning_rate: 0.001
      early_stopping_patience: 10
      validation_split: 0.2
      
    # 分类预测模型（专属特征）
    classification:
      base_model: "xgb"  # 基础模型类型
      n_estimators: 150
      max_depth: 5
      learning_rate: 0.1
      objective: "multi:softprob"
      num_class: 10
      eval_metric: "mlogloss"
      early_stopping_rounds: 15
      
    # 约束优化模型（专属特征）
    constraint:
      base_model: "ensemble"  # 基于集成模型
      optimization_method: "dual_constraint"
      constraint_weights:
        position: 0.4
        sum: 0.4
        pattern: 0.2
      optimization_iterations: 100
      convergence_threshold: 0.001
      
    # 集成融合模型
    ensemble:
      base_models: ["xgb", "lgb", "lstm", "classification"]
      weights:
        xgb: 0.3
        lgb: 0.3
        lstm: 0.2
        classification: 0.2
      dynamic_weights: true
      weight_update_frequency: 10  # 每10次预测更新一次权重
      performance_window: 50       # 性能评估窗口

# 特征工程配置
feature_engineering:
  # 基础特征
  basic_features:
    enable_position_features: true    # 位置特征
    enable_sum_features: true         # 和值特征
    enable_span_features: true        # 跨度特征
    enable_pattern_features: true     # 模式特征
    
  # 时序特征
  temporal_features:
    enable_lag_features: true         # 滞后特征
    lag_periods: [1, 2, 3, 5, 7, 10] # 滞后期数
    enable_rolling_features: true     # 滚动特征
    rolling_windows: [3, 5, 7, 10, 15] # 滚动窗口
    
  # 统计特征
  statistical_features:
    enable_frequency_features: true   # 频次特征
    enable_correlation_features: true # 相关性特征
    enable_distribution_features: true # 分布特征
    
  # 跨度专属特征
  span_specific_features:
    enable_pattern_encoding: true     # 模式编码
    enable_constraint_features: true  # 约束特征
    enable_correlation_matrix: true   # 相关性矩阵
    
    # 模式特征配置
    pattern_features:
      ascending_encoding: "binary"    # 升序编码方式
      descending_encoding: "binary"   # 降序编码方式
      same_digit_encoding: "count"    # 相同数字编码方式
      consecutive_encoding: "binary"  # 连续编码方式

# 训练配置
training:
  # 数据分割
  train_ratio: 0.7
  validation_ratio: 0.15
  test_ratio: 0.15
  
  # 训练参数
  cross_validation:
    enabled: true
    folds: 5
    shuffle: true
    random_state: 42
    
  # 早停配置
  early_stopping:
    enabled: true
    patience: 15
    min_delta: 0.001
    restore_best_weights: true
    
  # 模型保存
  model_saving:
    save_best_only: true
    save_frequency: 10  # 每10个epoch保存一次
    checkpoint_dir: "models/span_predictor/checkpoints"

# 评估配置
evaluation:
  # 评估指标
  metrics:
    regression_metrics: ["mae", "rmse", "r2_score", "accuracy_1", "accuracy_2"]
    classification_metrics: ["accuracy", "top_3_accuracy", "f1_score"]
    pattern_metrics: ["pattern_accuracy", "ascending_acc", "descending_acc", "same_digit_acc", "consecutive_acc"]
    constraint_metrics: ["dual_constraint_score", "position_consistency", "sum_consistency"]
    
  # 性能阈值
  performance_thresholds:
    mae: 1.0              # 平均绝对误差阈值
    rmse: 1.5             # 均方根误差阈值
    accuracy_1: 0.6       # ±1准确率阈值
    accuracy_2: 0.85      # ±2准确率阈值
    classification_accuracy: 0.4  # 分类准确率阈值
    pattern_accuracy: 0.5         # 模式准确率阈值
    dual_constraint_score: 0.7    # 双重约束分数阈值

# 预测配置
prediction:
  # 预测参数
  confidence_threshold: 0.6    # 置信度阈值
  prediction_range_expansion: 1 # 预测范围扩展
  
  # 约束优化
  constraint_optimization:
    enabled: true
    max_iterations: 50
    convergence_tolerance: 0.01
    
  # 输出配置
  output:
    include_probabilities: true      # 包含概率分布
    include_pattern_analysis: true   # 包含模式分析
    include_constraint_scores: true  # 包含约束分数
    include_confidence_intervals: true # 包含置信区间

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_handler:
    enabled: true
    filename: "logs/span_predictor.log"
    max_bytes: 10485760  # 10MB
    backup_count: 5
  console_handler:
    enabled: true
    level: "INFO"

# 缓存配置
caching:
  enabled: true
  cache_dir: "cache/span_predictor"
  feature_cache_ttl: 3600      # 特征缓存TTL（秒）
  model_cache_ttl: 86400       # 模型缓存TTL（秒）
  prediction_cache_ttl: 1800   # 预测缓存TTL（秒）

# 监控配置
monitoring:
  enabled: true
  metrics_collection_interval: 300  # 5分钟
  performance_alert_thresholds:
    prediction_time: 1.0  # 预测时间阈值（秒）
    memory_usage: 0.8     # 内存使用率阈值
    error_rate: 0.05      # 错误率阈值
