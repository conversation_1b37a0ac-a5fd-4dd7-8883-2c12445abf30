#!/usr/bin/env python3
"""
P8智能交集融合系统主类

集成所有融合组件，提供统一的预测接口
实现福彩3D预测器的智能融合和最终预测生成

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
import yaml
import json
import uuid
from datetime import datetime
from pathlib import Path

# 导入所有融合组件
from ..predictors.unified_predictor_interface import UnifiedPredictorInterface
from ..predictors.data_format_standard import DataFormatStandardizer, StandardPredictionResult
from .probability_fusion_engine import ProbabilityFusionEngine
from .constraint_optimizer import ConstraintOptimizer
from .intelligent_ranker import IntelligentRanker, RankingResult
from .dynamic_weight_adjuster import DynamicWeightAdjuster, PerformanceRecord
from ..data.fusion_data_access import FusionDataAccess

class FusionPredictor:
    """P8智能交集融合系统主类"""
    
    def __init__(self, db_path: str, config_path: Optional[str] = None):
        """
        初始化P8智能交集融合系统
        
        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        self.db_path = db_path
        self.config_path = config_path or "config/fusion_config.yaml"
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化所有组件
        self._initialize_components()
        
        # 系统状态
        self.is_initialized = True
        self.last_prediction_time = None
        self.prediction_count = 0
        
        self.logger.info("P8智能交集融合系统初始化完成")
    
    def _setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/fusion_system.log'),
                logging.StreamHandler()
            ]
        )
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.logger.info(f"加载配置文件: {self.config_path}")
            else:
                config = self._get_default_config()
                self.logger.warning(f"配置文件不存在，使用默认配置: {self.config_path}")
            
            return config
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'fusion': {
                'default_method': 'adaptive_fusion',
                'probability_weight': 0.5,
                'constraint_weight': 0.3,
                'diversity_weight': 0.2,
                'min_probability': 1e-6,
                'max_recommendations': 20
            },
            'ranking': {
                'default_strategy': 'adaptive',
                'top_k': 20,
                'min_score_threshold': 0.01,
                'diversity_penalty': 0.1
            },
            'weights': {
                'learning_rate': 0.1,
                'decay_factor': 0.95,
                'min_weight': 0.1,
                'max_weight': 2.0,
                'evaluation_window': 30
            },
            'constraints': {
                'sum_tolerance': 2.0,
                'span_tolerance': 1.0,
                'consistency_weight': 0.3,
                'diversity_weight': 0.2
            }
        }
    
    def _initialize_components(self):
        """初始化所有融合组件"""
        try:
            # 统一预测器接口
            self.unified_interface = UnifiedPredictorInterface(self.db_path)
            
            # 数据格式标准化器
            self.data_standardizer = DataFormatStandardizer()
            
            # 概率融合引擎
            self.fusion_engine = ProbabilityFusionEngine(self.config.get('fusion', {}))
            
            # 约束优化器
            self.constraint_optimizer = ConstraintOptimizer(self.config.get('constraints', {}))
            
            # 智能排序器
            self.intelligent_ranker = IntelligentRanker(self.config.get('ranking', {}))
            
            # 动态权重调整器
            self.weight_adjuster = DynamicWeightAdjuster(self.db_path, self.config.get('weights', {}))
            
            # 融合数据访问层
            self.data_access = FusionDataAccess(self.db_path)
            
            self.logger.info("所有融合组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            raise
    
    def load_predictors(self) -> bool:
        """加载所有预测器"""
        try:
            success = self.unified_interface.load_all_predictors()
            if success:
                self.logger.info("所有预测器加载成功")
            else:
                self.logger.warning("部分预测器加载失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"加载预测器失败: {e}")
            return False
    
    def predict_next_period(self, issue: str, 
                           fusion_method: Optional[str] = None,
                           ranking_strategy: Optional[str] = None,
                           top_k: Optional[int] = None) -> Dict[str, Any]:
        """
        预测下一期号码（核心方法）
        
        Args:
            issue: 期号
            fusion_method: 融合方法
            ranking_strategy: 排序策略
            top_k: 返回前K个结果
            
        Returns:
            预测结果字典
        """
        session_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始预测期号: {issue}, 会话ID: {session_id}")
            
            # 使用默认参数
            fusion_method = fusion_method or self.config['fusion']['default_method']
            ranking_strategy = ranking_strategy or self.config['ranking']['default_strategy']
            top_k = top_k or self.config['ranking']['top_k']
            
            # 步骤1: 获取所有预测器的预测结果
            raw_predictions = self.unified_interface.get_all_predictions(issue)
            
            # 步骤2: 标准化预测结果
            standardized_predictions = self._standardize_predictions(raw_predictions)
            
            # 步骤3: 获取约束信息
            constraint_info = self.unified_interface.get_constraint_matrix(issue)
            
            # 步骤4: 获取自适应权重
            adaptive_weights = self.weight_adjuster.get_adaptive_weights({
                'confidences': {name: pred.get('confidence', 0.5) for name, pred in raw_predictions.items()},
                'data_quality': self._assess_data_quality(raw_predictions)
            })
            
            # 步骤5: 概率融合
            fused_combinations = self.fusion_engine.fuse_probabilities(
                raw_predictions, adaptive_weights, fusion_method
            )
            
            # 步骤6: 约束优化
            optimized_combinations = self.constraint_optimizer.optimize_combinations(
                fused_combinations, constraint_info
            )
            
            # 步骤7: 智能排序
            ranking_results = self.intelligent_ranker.rank_combinations(
                optimized_combinations, constraint_info, ranking_strategy
            )
            
            # 步骤8: 生成最终结果
            final_results = self._generate_final_results(
                ranking_results[:top_k], issue, fusion_method, ranking_strategy
            )
            
            # 步骤9: 保存预测结果
            self._save_prediction_results(final_results, issue)
            
            # 步骤10: 记录融合会话
            execution_time = (datetime.now() - start_time).total_seconds()
            self._save_fusion_session(
                session_id, issue, fusion_method, ranking_strategy,
                raw_predictions, final_results, execution_time, True
            )
            
            # 更新统计
            self.prediction_count += 1
            self.last_prediction_time = datetime.now()
            
            self.logger.info(f"预测完成: {issue}, 生成{len(final_results)}个推荐")
            
            return {
                'issue': issue,
                'session_id': session_id,
                'predictions': final_results,
                'fusion_method': fusion_method,
                'ranking_strategy': ranking_strategy,
                'execution_time': execution_time,
                'total_combinations': len(optimized_combinations),
                'adaptive_weights': adaptive_weights,
                'constraint_summary': self.constraint_optimizer.get_constraint_summary(
                    optimized_combinations, constraint_info
                ),
                'ranking_summary': self.intelligent_ranker.get_ranking_summary(ranking_results),
                'timestamp': start_time.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            
            # 记录失败的会话
            execution_time = (datetime.now() - start_time).total_seconds()
            self._save_fusion_session(
                session_id, issue, fusion_method or 'unknown', 
                ranking_strategy or 'unknown', {}, {}, execution_time, False, str(e)
            )
            
            raise
    
    def _standardize_predictions(self, raw_predictions: Dict[str, Any]) -> Dict[str, StandardPredictionResult]:
        """标准化预测结果"""
        standardized = {}
        
        for name, pred in raw_predictions.items():
            if 'error' in pred:
                continue
                
            try:
                if name in ['hundreds', 'tens', 'units']:
                    result = self.data_standardizer.standardize_position_prediction(pred, name)
                elif name == 'sum':
                    result = self.data_standardizer.standardize_sum_prediction(pred, name)
                elif name == 'span':
                    result = self.data_standardizer.standardize_span_prediction(pred, name)
                else:
                    continue
                
                standardized[name] = result
                
            except Exception as e:
                self.logger.warning(f"标准化{name}预测失败: {e}")
        
        return standardized
    
    def _assess_data_quality(self, predictions: Dict[str, Any]) -> Dict[str, float]:
        """评估数据质量"""
        quality = {}
        
        for name, pred in predictions.items():
            if 'error' in pred:
                quality[name] = 0.0
            else:
                # 基于置信度和概率分布质量评估
                confidence = pred.get('confidence', 0.5)
                probs = pred.get('probabilities', [])
                
                if probs:
                    # 计算概率分布的熵（越低越好）
                    entropy = -sum(p * np.log2(p) for p in probs if p > 0)
                    max_entropy = np.log2(len(probs))
                    normalized_entropy = 1.0 - (entropy / max_entropy) if max_entropy > 0 else 0.5
                    
                    # 综合质量分数
                    quality[name] = (confidence + normalized_entropy) / 2
                else:
                    quality[name] = confidence
        
        return quality
    
    def _generate_final_results(self, ranking_results: List[RankingResult], 
                               issue: str, fusion_method: str, ranking_strategy: str) -> List[Dict[str, Any]]:
        """生成最终预测结果"""
        final_results = []
        
        for result in ranking_results:
            combo = result.combination
            if len(combo) == 3:
                h, t, u = int(combo[0]), int(combo[1]), int(combo[2])
                
                final_result = {
                    'rank': result.rank,
                    'hundreds': h,
                    'tens': t,
                    'units': u,
                    'combination': combo,
                    'sum_value': h + t + u,
                    'span_value': max(h, t, u) - min(h, t, u),
                    'combined_probability': result.probability,
                    'constraint_score': result.constraint_score,
                    'diversity_score': result.diversity_score,
                    'final_score': result.final_score,
                    'confidence_level': self._get_confidence_level(result.final_score),
                    'fusion_method': fusion_method,
                    'ranking_strategy': ranking_strategy,
                    'metadata': result.metadata
                }
                
                final_results.append(final_result)
        
        return final_results
    
    def _get_confidence_level(self, score: float) -> str:
        """根据分数确定置信水平"""
        if score >= 0.8:
            return 'high'
        elif score >= 0.5:
            return 'medium'
        else:
            return 'low'
    
    def _save_prediction_results(self, results: List[Dict[str, Any]], issue: str):
        """保存预测结果"""
        try:
            # 转换为数据库格式
            db_results = []
            for result in results:
                db_result = {
                    'rank': result['rank'],
                    'hundreds': result['hundreds'],
                    'tens': result['tens'],
                    'units': result['units'],
                    'sum_value': result['sum_value'],
                    'span_value': result['span_value'],
                    'combined_probability': result['combined_probability'],
                    'hundreds_prob': 0.1,  # 默认值，实际应从详细数据获取
                    'tens_prob': 0.1,
                    'units_prob': 0.1,
                    'sum_prob': 0.1,
                    'span_prob': 0.1,
                    'sum_consistency': result['constraint_score'],
                    'span_consistency': result['constraint_score'],
                    'constraint_score': result['constraint_score'],
                    'diversity_score': result['diversity_score'],
                    'confidence_level': result['confidence_level'],
                    'fusion_method': result['fusion_method'],
                    'ranking_strategy': result['ranking_strategy']
                }
                db_results.append(db_result)
            
            self.data_access.save_final_predictions(db_results, issue)
            
        except Exception as e:
            self.logger.error(f"保存预测结果失败: {e}")
    
    def _save_fusion_session(self, session_id: str, issue: str, fusion_method: str,
                            ranking_strategy: str, input_data: Dict[str, Any],
                            output_data: Dict[str, Any], execution_time: float,
                            success: bool, error_message: str = None):
        """保存融合会话记录"""
        try:
            self.data_access.save_fusion_session(
                session_id, issue, fusion_method, ranking_strategy,
                input_data, output_data, execution_time, success, error_message
            )
        except Exception as e:
            self.logger.error(f"保存融合会话失败: {e}")
    
    def evaluate_and_update_weights(self, actual_result: str, issue: str) -> Dict[str, Any]:
        """
        评估预测结果并更新权重

        Args:
            actual_result: 实际开奖结果 (如 "123")
            issue: 期号

        Returns:
            评估结果和权重更新信息
        """
        try:
            self.logger.info(f"开始评估期号 {issue} 的预测结果: {actual_result}")

            # 获取该期的预测结果
            predicted_results = self.data_access.get_final_predictions(issue)

            if not predicted_results:
                self.logger.warning(f"未找到期号 {issue} 的预测结果")
                return {'error': 'no_predictions_found'}

            # 转换为字典格式
            predicted_dicts = []
            for pred in predicted_results:
                pred_dict = {
                    'hundreds': pred.hundreds,
                    'tens': pred.tens,
                    'units': pred.units,
                    'sum_value': pred.sum_value,
                    'span_value': pred.span_value,
                    'combined_probability': pred.combined_probability,
                    'constraint_score': pred.constraint_score,
                    'fusion_method': pred.fusion_method,
                    'ranking_strategy': pred.ranking_strategy
                }
                predicted_dicts.append(pred_dict)

            # 保存性能评估
            evaluation_success = self.data_access.save_performance_evaluation(
                issue, actual_result, predicted_dicts
            )

            # 获取最新的性能数据
            performance_summary = self.data_access.get_performance_summary(days=30)

            # 创建性能记录用于权重更新
            performance_records = self._create_performance_records(
                issue, actual_result, predicted_dicts, performance_summary
            )

            # 更新权重
            updated_weights = self.weight_adjuster.update_weights(performance_records)

            # 保存更新后的权重
            self.data_access.save_fusion_weights(updated_weights, performance_summary)

            evaluation_result = {
                'issue': issue,
                'actual_result': actual_result,
                'evaluation_success': evaluation_success,
                'performance_summary': performance_summary,
                'updated_weights': updated_weights,
                'weight_changes': self._calculate_weight_changes(updated_weights),
                'timestamp': datetime.now().isoformat()
            }

            self.logger.info(f"评估和权重更新完成: {issue}")
            return evaluation_result

        except Exception as e:
            self.logger.error(f"评估和权重更新失败: {e}")
            return {'error': str(e)}

    def _create_performance_records(self, issue: str, actual_result: str,
                                   predicted_results: List[Dict[str, Any]],
                                   performance_summary: Dict[str, Any]) -> List[PerformanceRecord]:
        """创建性能记录"""
        records = []

        try:
            actual_h, actual_t, actual_u = int(actual_result[0]), int(actual_result[1]), int(actual_result[2])
            actual_sum = actual_h + actual_t + actual_u
            actual_span = max(actual_h, actual_t, actual_u) - min(actual_h, actual_t, actual_u)

            # 为每个预测器创建性能记录
            predictors = ['hundreds', 'tens', 'units', 'sum', 'span']

            for predictor_name in predictors:
                if predictor_name == 'hundreds':
                    predicted_value = predicted_results[0]['hundreds'] if predicted_results else 0
                    actual_value = actual_h
                    accuracy = 1.0 if predicted_value == actual_value else 0.0
                elif predictor_name == 'tens':
                    predicted_value = predicted_results[0]['tens'] if predicted_results else 0
                    actual_value = actual_t
                    accuracy = 1.0 if predicted_value == actual_value else 0.0
                elif predictor_name == 'units':
                    predicted_value = predicted_results[0]['units'] if predicted_results else 0
                    actual_value = actual_u
                    accuracy = 1.0 if predicted_value == actual_value else 0.0
                elif predictor_name == 'sum':
                    predicted_value = predicted_results[0]['sum_value'] if predicted_results else 0
                    actual_value = actual_sum
                    accuracy = 1.0 if abs(predicted_value - actual_value) <= 2 else 0.0
                elif predictor_name == 'span':
                    predicted_value = predicted_results[0]['span_value'] if predicted_results else 0
                    actual_value = actual_span
                    accuracy = 1.0 if abs(predicted_value - actual_value) <= 1 else 0.0

                # 获取置信度（从性能摘要或默认值）
                confidence = performance_summary.get(f'{predictor_name}_accuracy_rate', 0.5)

                record = PerformanceRecord(
                    predictor_name=predictor_name,
                    issue=issue,
                    predicted_value=predicted_value,
                    actual_value=actual_value,
                    accuracy=accuracy,
                    confidence=confidence,
                    timestamp=datetime.now()
                )

                records.append(record)

            return records

        except Exception as e:
            self.logger.error(f"创建性能记录失败: {e}")
            return []

    def _calculate_weight_changes(self, new_weights: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """计算权重变化"""
        try:
            current_weights = self.data_access.get_fusion_weights()
            changes = {}

            for name, new_weight in new_weights.items():
                old_weight = current_weights.get(name, 1.0)
                absolute_change = new_weight - old_weight
                relative_change = (absolute_change / old_weight) if old_weight != 0 else 0

                changes[name] = {
                    'old_weight': old_weight,
                    'new_weight': new_weight,
                    'absolute_change': absolute_change,
                    'relative_change': relative_change
                }

            return changes

        except Exception as e:
            self.logger.error(f"计算权重变化失败: {e}")
            return {}

    def get_fusion_summary(self, days: int = 7) -> Dict[str, Any]:
        """
        获取融合系统摘要

        Args:
            days: 统计天数

        Returns:
            融合系统摘要
        """
        try:
            # 获取性能摘要
            performance_summary = self.data_access.get_performance_summary(days)

            # 获取最近的会话
            recent_sessions = self.data_access.get_recent_sessions(limit=10)

            # 获取权重摘要
            weight_summary = self.weight_adjuster.get_weight_summary()

            # 获取系统状态
            system_status = self.get_system_status()

            # 计算成功率
            success_rate = 0.0
            if recent_sessions:
                successful_sessions = sum(1 for s in recent_sessions if s['success'])
                success_rate = successful_sessions / len(recent_sessions)

            # 计算平均执行时间
            avg_execution_time = 0.0
            if recent_sessions:
                execution_times = [s['execution_time'] for s in recent_sessions if s['execution_time']]
                if execution_times:
                    avg_execution_time = sum(execution_times) / len(execution_times)

            summary = {
                'period_days': days,
                'performance_summary': performance_summary,
                'recent_sessions_count': len(recent_sessions),
                'success_rate': success_rate,
                'avg_execution_time': avg_execution_time,
                'weight_summary': weight_summary,
                'system_status': system_status,
                'most_used_fusion_method': self._get_most_used_method(recent_sessions, 'fusion_method'),
                'most_used_ranking_strategy': self._get_most_used_method(recent_sessions, 'ranking_strategy'),
                'timestamp': datetime.now().isoformat()
            }

            return summary

        except Exception as e:
            self.logger.error(f"获取融合摘要失败: {e}")
            return {'error': str(e)}

    def _get_most_used_method(self, sessions: List[Dict[str, Any]], field: str) -> str:
        """获取最常用的方法"""
        if not sessions:
            return 'unknown'

        method_counts = {}
        for session in sessions:
            method = session.get(field, 'unknown')
            method_counts[method] = method_counts.get(method, 0) + 1

        return max(method_counts.items(), key=lambda x: x[1])[0] if method_counts else 'unknown'

    def predict_batch(self, issues: List[str],
                     fusion_method: Optional[str] = None,
                     ranking_strategy: Optional[str] = None,
                     top_k: Optional[int] = None) -> Dict[str, Dict[str, Any]]:
        """
        批量预测多个期号

        Args:
            issues: 期号列表
            fusion_method: 融合方法
            ranking_strategy: 排序策略
            top_k: 返回前K个结果

        Returns:
            批量预测结果字典
        """
        batch_results = {}

        for issue in issues:
            try:
                result = self.predict_next_period(issue, fusion_method, ranking_strategy, top_k)
                batch_results[issue] = result
                self.logger.info(f"批量预测完成: {issue}")

            except Exception as e:
                self.logger.error(f"批量预测失败: {issue}, 错误: {e}")
                batch_results[issue] = {'error': str(e)}

        return batch_results

    def get_prediction_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取预测历史

        Args:
            limit: 返回数量限制

        Returns:
            预测历史列表
        """
        try:
            # 获取最新的预测结果
            latest_predictions = self.data_access.get_latest_predictions(limit)

            history = []
            for pred in latest_predictions:
                history_item = {
                    'issue': pred.issue,
                    'rank': pred.prediction_rank,
                    'combination': f"{pred.hundreds}{pred.tens}{pred.units}",
                    'sum_value': pred.sum_value,
                    'span_value': pred.span_value,
                    'probability': pred.combined_probability,
                    'constraint_score': pred.constraint_score,
                    'confidence_level': pred.confidence_level,
                    'fusion_method': pred.fusion_method,
                    'ranking_strategy': pred.ranking_strategy,
                    'created_at': pred.created_at
                }
                history.append(history_item)

            return history

        except Exception as e:
            self.logger.error(f"获取预测历史失败: {e}")
            return []

    def validate_prediction_input(self, issue: str) -> Dict[str, Any]:
        """
        验证预测输入

        Args:
            issue: 期号

        Returns:
            验证结果
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }

        try:
            # 验证期号格式
            if not issue or len(issue) < 6:
                validation_result['valid'] = False
                validation_result['errors'].append('期号格式无效')

            # 检查预测器状态
            predictor_status = self.unified_interface.get_predictor_status()

            loaded_count = sum(1 for status in predictor_status.values() if status.get('loaded', False))
            trained_count = sum(1 for status in predictor_status.values() if status.get('trained', False))

            if loaded_count < 3:
                validation_result['valid'] = False
                validation_result['errors'].append(f'预测器加载不足: {loaded_count}/5')

            if trained_count < 3:
                validation_result['warnings'].append(f'部分预测器未训练: {trained_count}/5')

            # 检查数据库连接
            try:
                self.data_access.get_performance_summary(days=1)
            except Exception as e:
                validation_result['valid'] = False
                validation_result['errors'].append(f'数据库连接失败: {e}')

            return validation_result

        except Exception as e:
            self.logger.error(f"验证预测输入失败: {e}")
            return {
                'valid': False,
                'errors': [str(e)],
                'warnings': []
            }

    def optimize_fusion_parameters(self, historical_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        优化融合参数

        Args:
            historical_data: 历史数据

        Returns:
            优化结果
        """
        try:
            self.logger.info("开始优化融合参数")

            # 获取历史性能数据
            if not historical_data:
                historical_data = self.data_access.get_performance_summary(days=30)

            # 基于历史性能调整参数
            optimization_result = {
                'original_config': self.config.copy(),
                'optimized_config': self.config.copy(),
                'improvements': {},
                'timestamp': datetime.now().isoformat()
            }

            # 根据命中率调整权重
            if 'exact_hit_rate' in historical_data:
                hit_rate = historical_data['exact_hit_rate']

                if hit_rate < 0.1:  # 命中率低，增加约束权重
                    optimization_result['optimized_config']['fusion']['constraint_weight'] *= 1.2
                    optimization_result['optimized_config']['fusion']['probability_weight'] *= 0.9
                    optimization_result['improvements']['constraint_weight'] = 'increased due to low hit rate'

                elif hit_rate > 0.3:  # 命中率高，增加概率权重
                    optimization_result['optimized_config']['fusion']['probability_weight'] *= 1.1
                    optimization_result['optimized_config']['fusion']['constraint_weight'] *= 0.95
                    optimization_result['improvements']['probability_weight'] = 'increased due to high hit rate'

            # 根据多样性调整参数
            if 'avg_overall_score' in historical_data:
                avg_score = historical_data['avg_overall_score']

                if avg_score < 0.3:  # 整体分数低，增加多样性
                    optimization_result['optimized_config']['fusion']['diversity_weight'] *= 1.15
                    optimization_result['improvements']['diversity_weight'] = 'increased due to low overall score'

            # 更新配置
            self.config = optimization_result['optimized_config']

            self.logger.info("融合参数优化完成")
            return optimization_result

        except Exception as e:
            self.logger.error(f"优化融合参数失败: {e}")
            return {'error': str(e)}

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            predictor_status = self.unified_interface.get_predictor_status()
            performance_summary = self.data_access.get_performance_summary()
            weight_summary = self.weight_adjuster.get_weight_summary()

            return {
                'system_initialized': self.is_initialized,
                'prediction_count': self.prediction_count,
                'last_prediction_time': self.last_prediction_time.isoformat() if self.last_prediction_time else None,
                'predictor_status': predictor_status,
                'performance_summary': performance_summary,
                'weight_summary': weight_summary,
                'config': self.config
            }

        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {'error': str(e)}
