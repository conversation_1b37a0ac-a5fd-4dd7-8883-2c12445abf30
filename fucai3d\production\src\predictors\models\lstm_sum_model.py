#!/usr/bin/env python3
"""
LSTM和值预测模型

实现基于LSTM的和值回归预测模型
捕获时序依赖，包括时序数据准备、LSTM网络构建、训练和预测

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
import pickle
import logging
from typing import Dict, List, Tuple, Optional, Any
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import MinMaxScaler
import sqlite3
import json
from pathlib import Path

class LSTMSumModel:
    """LSTM和值预测模型"""
    
    def __init__(self, db_path: str, config: Optional[Dict] = None):
        """
        初始化LSTM和值模型
        
        Args:
            db_path: 数据库路径
            config: 模型配置参数
        """
        self.db_path = db_path
        self.model = None
        self.is_trained = False
        self.scaler = MinMaxScaler()
        self.logger = logging.getLogger("LSTMSumModel")
        
        # 默认模型参数
        self.model_params = {
            'sequence_length': 20,
            'hidden_units': 64,
            'dropout_rate': 0.2,
            'epochs': 100,
            'batch_size': 32,
            'learning_rate': 0.001,
            'validation_split': 0.2
        }
        
        # 更新配置参数
        if config and 'lstm' in config:
            self.model_params.update(config['lstm'])
    
    def build_model(self):
        """构建LSTM回归模型"""
        try:
            import tensorflow as tf
            from tensorflow.keras.models import Sequential
            from tensorflow.keras.layers import LSTM, Dense, Dropout
            from tensorflow.keras.optimizers import Adam
            
            model = Sequential([
                LSTM(self.model_params['hidden_units'], 
                     return_sequences=True,
                     input_shape=(self.model_params['sequence_length'], 1)),
                Dropout(self.model_params['dropout_rate']),
                
                LSTM(self.model_params['hidden_units'] // 2, 
                     return_sequences=False),
                Dropout(self.model_params['dropout_rate']),
                
                Dense(32, activation='relu'),
                Dropout(self.model_params['dropout_rate']),
                
                Dense(1, activation='linear')  # 回归输出
            ])
            
            model.compile(
                optimizer=Adam(learning_rate=self.model_params['learning_rate']),
                loss='mse',
                metrics=['mae']
            )
            
            self.model = model
            self.logger.info("LSTM和值模型构建成功")
            return self.model
            
        except ImportError:
            error_msg = "TensorFlow未安装，请安装: pip install tensorflow"
            self.logger.error(error_msg)
            raise ImportError(error_msg)
    
    def prepare_sequences(self, data: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备LSTM时序数据
        
        Args:
            data: 和值序列数据
            sequence_length: 序列长度
            
        Returns:
            X序列和y目标
        """
        X, y = [], []
        
        for i in range(sequence_length, len(data)):
            X.append(data[i-sequence_length:i])
            y.append(data[i])
        
        return np.array(X), np.array(y)
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """从数据库加载训练数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 查询彩票数据
            query = """
                SELECT hundreds, tens, units, issue, draw_date
                FROM lottery_data
                ORDER BY draw_date, issue
            """
            
            data = pd.read_sql_query(query, conn)
            conn.close()
            
            if data.empty:
                raise ValueError("没有找到彩票数据")
            
            # 计算和值
            sum_values = (data['hundreds'] + data['tens'] + data['units']).values
            
            self.logger.info(f"加载了 {len(sum_values)} 条和值数据")
            
            # 数据标准化
            sum_values_scaled = self.scaler.fit_transform(sum_values.reshape(-1, 1)).flatten()
            
            # 准备时序数据
            X, y = self.prepare_sequences(sum_values_scaled, self.model_params['sequence_length'])
            
            # 重塑X为LSTM输入格式 (samples, timesteps, features)
            X = X.reshape((X.shape[0], X.shape[1], 1))
            
            self.logger.info(f"生成了 {len(X)} 个时序样本，序列长度: {X.shape[1]}")
            
            return X, y
            
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            raise
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        训练LSTM模型
        
        Args:
            X: 特征序列，如果为None则从数据库加载
            y: 目标向量，如果为None则从数据库加载
            
        Returns:
            训练性能指标
        """
        try:
            # 如果没有提供数据，从数据库加载
            if X is None or y is None:
                X, y = self.load_data()
            
            # 构建模型
            if self.model is None:
                self.build_model()
            
            # 分割训练集和验证集
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            self.logger.info(f"训练集大小: {len(X_train)}, 验证集大小: {len(X_val)}")
            
            # 训练模型
            try:
                from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
                
                callbacks = [
                    EarlyStopping(
                        monitor='val_loss',
                        patience=20,
                        restore_best_weights=True
                    ),
                    ReduceLROnPlateau(
                        monitor='val_loss',
                        factor=0.5,
                        patience=10,
                        min_lr=1e-6
                    )
                ]
                
                history = self.model.fit(
                    X_train, y_train,
                    epochs=self.model_params['epochs'],
                    batch_size=self.model_params['batch_size'],
                    validation_data=(X_val, y_val),
                    callbacks=callbacks,
                    verbose=0
                )
                
            except ImportError:
                # 如果没有callbacks，简单训练
                history = self.model.fit(
                    X_train, y_train,
                    epochs=self.model_params['epochs'],
                    batch_size=self.model_params['batch_size'],
                    validation_data=(X_val, y_val),
                    verbose=0
                )
            
            self.is_trained = True
            
            # 评估模型
            performance = self.evaluate(X_val, y_val)
            
            self.logger.info(f"LSTM和值模型训练完成，验证集性能: {performance}")
            
            return performance
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        预测和值
        
        Args:
            X: 特征序列
            
        Returns:
            预测的和值数组
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 预测标准化后的值
            predictions_scaled = self.model.predict(X, verbose=0)
            
            # 反标准化
            predictions = self.scaler.inverse_transform(predictions_scaled).flatten()
            
            # 应用约束：确保预测值在[0,27]范围内
            predictions = np.clip(predictions, 0, 27)
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测和值及置信度
        
        Args:
            X: 特征序列
            
        Returns:
            预测值和置信度数组
        """
        predictions = self.predict(X)
        
        # 基于预测稳定性的置信度计算
        confidences = []
        for pred in predictions:
            # 基于预测值与理论中位数(13.5)的距离计算置信度
            distance = abs(pred - 13.5)
            confidence = max(0.1, min(0.9, 1.0 - distance / 13.5))
            confidences.append(confidence)
        
        return predictions, np.array(confidences)
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            X_test: 测试特征序列
            y_test: 测试目标
            
        Returns:
            性能指标字典
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 预测标准化后的值
            y_pred_scaled = self.model.predict(X_test, verbose=0).flatten()
            
            # 反标准化预测值和真实值
            y_pred = self.scaler.inverse_transform(y_pred_scaled.reshape(-1, 1)).flatten()
            y_true = self.scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
            
            # 应用约束
            y_pred = np.clip(y_pred, 0, 27)
            
            # 计算各种误差指标
            mae = mean_absolute_error(y_true, y_pred)
            rmse = np.sqrt(mean_squared_error(y_true, y_pred))
            r2 = r2_score(y_true, y_pred)
            
            # 计算不同精度的准确率
            accuracy_1 = np.mean(np.abs(y_true - y_pred) <= 1)
            accuracy_2 = np.mean(np.abs(y_true - y_pred) <= 2)
            
            return {
                'mae': mae,
                'rmse': rmse,
                'r2_score': r2,
                'accuracy_1': accuracy_1,
                'accuracy_2': accuracy_2,
                'accuracy': accuracy_1  # 主要准确率指标
            }
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            raise
    
    def predict_next_sequence(self, recent_data: np.ndarray, steps: int = 1) -> np.ndarray:
        """
        预测未来多步
        
        Args:
            recent_data: 最近的和值数据
            steps: 预测步数
            
        Returns:
            未来steps步的预测值
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            # 标准化输入数据
            recent_scaled = self.scaler.transform(recent_data.reshape(-1, 1)).flatten()
            
            # 准备初始序列
            sequence = recent_scaled[-self.model_params['sequence_length']:].copy()
            predictions = []
            
            for _ in range(steps):
                # 重塑为LSTM输入格式
                X_input = sequence.reshape(1, len(sequence), 1)
                
                # 预测下一个值
                pred_scaled = self.model.predict(X_input, verbose=0)[0, 0]
                
                # 反标准化
                pred = self.scaler.inverse_transform([[pred_scaled]])[0, 0]
                pred = np.clip(pred, 0, 27)
                
                predictions.append(pred)
                
                # 更新序列（移除第一个，添加预测值）
                sequence = np.append(sequence[1:], pred_scaled)
            
            return np.array(predictions)
            
        except Exception as e:
            self.logger.error(f"多步预测失败: {e}")
            raise
    
    def save_model(self, filepath: str) -> bool:
        """
        保存模型
        
        Args:
            filepath: 保存路径
            
        Returns:
            保存是否成功
        """
        if not self.is_trained:
            self.logger.warning("模型尚未训练，无法保存")
            return False
        
        try:
            # 确保目录存在
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存Keras模型
            model_path = filepath.replace('.pkl', '_keras.h5')
            self.model.save(model_path)
            
            # 保存其他数据
            model_data = {
                'model_params': self.model_params,
                'scaler': self.scaler,
                'is_trained': self.is_trained,
                'model_path': model_path
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"模型保存成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            加载是否成功
        """
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model_params = model_data['model_params']
            self.scaler = model_data['scaler']
            self.is_trained = model_data['is_trained']
            
            # 加载Keras模型
            try:
                import tensorflow as tf
                model_path = model_data['model_path']
                self.model = tf.keras.models.load_model(model_path)
            except ImportError:
                self.logger.error("TensorFlow未安装，无法加载LSTM模型")
                return False
            
            self.logger.info(f"模型加载成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False
