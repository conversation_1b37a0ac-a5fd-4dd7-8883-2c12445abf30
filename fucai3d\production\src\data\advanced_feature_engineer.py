"""
P2高级特征工程系统
基于P1基础特征系统，提供高级特征工程能力，包括：
- 智能缓存优化
- 专用特征体系
- ML就绪数据准备
- 与Feature-engine库集成
"""

import sqlite3
import json
import threading
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import pandas as pd
import numpy as np
from collections import OrderedDict

try:
    from .feature_service import FeatureService
    from .feature_calculator import LotteryFeatureCalculator
    from .predictor_features import (
        generate_hundreds_features,
        generate_tens_features,
        generate_units_features,
        generate_sum_features,
        generate_span_features,
        generate_common_features
    )
except ImportError:
    # 绝对导入作为备选方案
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from data.feature_service import FeatureService
    from data.feature_calculator import LotteryFeatureCalculator
    from data.predictor_features import (
        generate_hundreds_features,
        generate_tens_features,
        generate_units_features,
        generate_sum_features,
        generate_span_features,
        generate_common_features
    )


class AdvancedFeatureEngineer:
    """P2高级特征工程引擎"""
    
    def __init__(self, db_path: str, cache_enabled: bool = True, cache_size: int = 1000):
        """
        初始化高级特征工程引擎
        
        Args:
            db_path: 数据库文件路径
            cache_enabled: 是否启用缓存
            cache_size: 内存缓存大小
        """
        self.db_path = db_path
        self.cache_enabled = cache_enabled
        self.cache_size = cache_size
        
        # 初始化基础服务
        self.feature_service = FeatureService(db_path)
        self.calculator = LotteryFeatureCalculator()
        
        # 初始化缓存系统
        self._memory_cache = OrderedDict()
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'total_requests': 0
        }
        self._cache_lock = threading.Lock()
        
        # 特征类型映射
        self.feature_types = {
            'hundreds': '百位专用特征',
            'tens': '十位专用特征', 
            'units': '个位专用特征',
            'sum': '和值专用特征',
            'span': '跨度专用特征',
            'common': '通用特征'
        }
    
    def get_features_with_cache(self, issue: str, feature_type: str = 'all') -> Optional[Dict[str, Any]]:
        """
        获取带缓存的特征数据
        
        Args:
            issue: 期号
            feature_type: 特征类型 ('hundreds', 'tens', 'units', 'sum', 'span', 'common', 'all')
            
        Returns:
            Optional[Dict[str, Any]]: 特征字典
        """
        if not self.cache_enabled:
            return self._calculate_features_real_time(issue, feature_type)
        
        # 生成缓存键
        cache_key = f"{issue}_{feature_type}"
        
        with self._cache_lock:
            self._cache_stats['total_requests'] += 1
            
            # 检查内存缓存
            if cache_key in self._memory_cache:
                # 移动到末尾（LRU策略）
                self._memory_cache.move_to_end(cache_key)
                self._cache_stats['hits'] += 1
                return self._memory_cache[cache_key].copy()
            
            self._cache_stats['misses'] += 1
        
        # 缓存未命中，实时计算
        features = self._calculate_features_real_time(issue, feature_type)
        
        if features:
            self._save_to_cache(cache_key, features)
        
        return features
    
    def _calculate_features_real_time(self, issue: str, feature_type: str) -> Optional[Dict[str, Any]]:
        """
        实时计算特征
        
        Args:
            issue: 期号
            feature_type: 特征类型
            
        Returns:
            Optional[Dict[str, Any]]: 特征字典
        """
        # 获取基础特征
        base_features = self.feature_service.get_features_for_issue(issue, include_trend=True)
        if not base_features:
            return None
        
        # 根据特征类型过滤和扩展特征
        if feature_type == 'all':
            return self._generate_all_advanced_features(base_features, issue)
        else:
            return self._generate_specific_features(base_features, feature_type, issue)
    
    def _generate_all_advanced_features(self, base_features: Dict[str, Any], issue: str) -> Dict[str, Any]:
        """生成所有高级特征"""
        advanced_features = base_features.copy()
        
        # 添加各类专用特征
        for feature_type in ['hundreds', 'tens', 'units', 'sum', 'span']:
            specific_features = self._generate_specific_features(base_features, feature_type, issue)
            if specific_features:
                # 添加前缀以区分特征类型
                for key, value in specific_features.items():
                    if not key.startswith(feature_type):
                        advanced_features[f"{feature_type}_{key}"] = value
        
        return advanced_features
    
    def _generate_specific_features(self, base_features: Dict[str, Any],
                                  feature_type: str, issue: str) -> Dict[str, Any]:
        """
        生成特定类型的专用特征

        Args:
            base_features: 基础特征
            feature_type: 特征类型
            issue: 期号

        Returns:
            Dict[str, Any]: 专用特征字典
        """
        try:
            # 获取历史数据用于特征生成
            historical_data = self.feature_service.get_historical_features(
                limit=100, include_trend=True
            )

            if not historical_data:
                return base_features

            # 转换为DataFrame
            df = pd.DataFrame(historical_data)

            # 确保必要的列存在
            required_cols = ['hundreds', 'tens', 'units']
            if not all(col in df.columns for col in required_cols):
                return base_features

            # 根据特征类型生成专用特征
            if feature_type == 'hundreds':
                enhanced_df = generate_hundreds_features(df, window_sizes=[5, 10, 20])
            elif feature_type == 'tens':
                enhanced_df = generate_tens_features(df, window_sizes=[5, 10, 20])
            elif feature_type == 'units':
                enhanced_df = generate_units_features(df, window_sizes=[5, 10, 20])
            elif feature_type == 'sum':
                enhanced_df = generate_sum_features(df, window_sizes=[5, 10, 20])
            elif feature_type == 'span':
                enhanced_df = generate_span_features(df, window_sizes=[5, 10, 20])
            elif feature_type == 'common':
                enhanced_df = generate_common_features(df, window_sizes=[5, 10, 20])
            else:
                return base_features

            # 获取最新一行的特征（对应当前期号）
            if len(enhanced_df) > 0:
                latest_features = enhanced_df.iloc[-1].to_dict()
                # 合并基础特征和专用特征
                specific_features = base_features.copy()
                specific_features.update(latest_features)
                return specific_features

        except Exception as e:
            # 如果专用特征生成失败，返回基础特征
            print(f"专用特征生成失败 ({feature_type}): {e}")

        return base_features
    
    def _save_to_cache(self, cache_key: str, features: Dict[str, Any]):
        """保存到缓存"""
        with self._cache_lock:
            # LRU缓存管理
            if len(self._memory_cache) >= self.cache_size:
                # 删除最旧的条目
                self._memory_cache.popitem(last=False)
            
            self._memory_cache[cache_key] = features.copy()
    
    def prepare_ml_training_data(self, feature_types: List[str], 
                               start_issue: Optional[str] = None,
                               end_issue: Optional[str] = None,
                               limit: Optional[int] = None) -> pd.DataFrame:
        """
        准备ML训练数据
        
        Args:
            feature_types: 特征类型列表
            start_issue: 起始期号
            end_issue: 结束期号
            limit: 限制数量
            
        Returns:
            pd.DataFrame: ML就绪的特征数据
        """
        # 获取历史数据
        historical_features = self.feature_service.get_historical_features(
            start_issue, end_issue, limit, include_trend=True
        )
        
        if not historical_features:
            return pd.DataFrame()
        
        # 转换为DataFrame
        df = pd.DataFrame(historical_features)
        
        # 根据特征类型过滤列
        if 'all' not in feature_types:
            selected_columns = ['issue']  # 保留期号列
            for feature_type in feature_types:
                # 选择对应类型的特征列
                type_columns = [col for col in df.columns if col.startswith(feature_type)]
                selected_columns.extend(type_columns)
            
            # 确保选择的列存在
            selected_columns = [col for col in selected_columns if col in df.columns]
            df = df[selected_columns]
        
        return df
    
    def clear_cache(self):
        """清空缓存"""
        with self._cache_lock:
            self._memory_cache.clear()
            self._cache_stats = {
                'hits': 0,
                'misses': 0,
                'total_requests': 0
            }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._cache_lock:
            hit_rate = 0
            if self._cache_stats['total_requests'] > 0:
                hit_rate = self._cache_stats['hits'] / self._cache_stats['total_requests']
            
            return {
                'cache_enabled': self.cache_enabled,
                'cache_size': len(self._memory_cache),
                'max_cache_size': self.cache_size,
                'hit_rate': hit_rate,
                'total_requests': self._cache_stats['total_requests'],
                'cache_hits': self._cache_stats['hits'],
                'cache_misses': self._cache_stats['misses']
            }
    
    def get_available_feature_types(self) -> Dict[str, str]:
        """获取可用的特征类型"""
        return self.feature_types.copy()
    
    def validate_system(self) -> Dict[str, Any]:
        """验证系统状态"""
        # 验证基础系统
        base_validation = self.feature_service.validate_database()
        
        # 添加高级系统验证
        advanced_validation = {
            'advanced_feature_engine': 'initialized',
            'cache_system': 'enabled' if self.cache_enabled else 'disabled',
            'feature_types_available': len(self.feature_types),
            'cache_stats': self.get_cache_stats()
        }
        
        return {
            'base_system': base_validation,
            'advanced_system': advanced_validation,
            'overall_status': 'ready' if base_validation.get('status') == 'success' else 'error'
        }
