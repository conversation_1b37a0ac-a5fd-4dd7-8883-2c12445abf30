#!/usr/bin/env python3
"""
P6和值预测器主类

基于BaseIndependentPredictor架构，实现和值预测功能
支持6种模型：XGBoost、LightGBM、LSTM、分布预测、约束优化、集成融合

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import sqlite3
import json
import logging
import yaml
from pathlib import Path
from datetime import datetime
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.model_selection import train_test_split

# 导入统一基类
from .base_independent_predictor import BaseIndependentPredictor
from ..data.sum_data_access import SumDataAccess

# 导入所有模型
from .models.xgb_sum_model import XGBSumModel
from .models.lgb_sum_model import LGBSumModel
from .models.lstm_sum_model import LSTMSumModel
from .models.distribution_sum_model import DistributionSumModel
from .models.constraint_sum_model import ConstraintSumModel
from .models.ensemble_sum_model import EnsembleSumModel

class SumPredictor(BaseIndependentPredictor):
    """P6和值预测器主类"""

    def __init__(self, db_path: str, config_path: Optional[str] = None):
        """
        初始化和值预测器

        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        # 继承BaseIndependentPredictor，position设为'sum'
        super().__init__('sum', db_path)

        # 和值预测器专属属性
        self.data_access = SumDataAccess(db_path)
        self.config = self._load_config(config_path)

        # 和值预测的特殊配置
        self.prediction_range = (0, 27)  # 和值范围0-27
        self.target_type = 'regression'  # 回归问题

        # 初始化所有模型
        self.models = {}
        self.current_model = 'ensemble'  # 默认使用集成模型

        # 位置预测器引用（用于约束优化）
        self.position_predictors = {
            'hundreds': None,
            'tens': None,
            'units': None
        }

        self.logger.info("初始化和值预测器完成")

    def _load_config(self, config_path: Optional[str]) -> Dict:
        """加载配置文件"""
        if config_path is None:
            config_path = "config/sum_predictor_config.yaml"

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            self.logger.warning(f"加载配置文件失败: {e}，使用默认配置")
            return {}
    
    def build_model(self):
        """构建所有模型"""
        try:
            # 初始化所有6种模型
            self.models['xgb'] = XGBSumModel(self.db_path, self.config)
            self.models['lgb'] = LGBSumModel(self.db_path, self.config)
            self.models['lstm'] = LSTMSumModel(self.db_path, self.config)
            self.models['distribution'] = DistributionSumModel(self.db_path, self.config)
            self.models['constraint'] = ConstraintSumModel(self.db_path, self.config)
            self.models['ensemble'] = EnsembleSumModel(self.db_path, self.config)

            # 为约束模型设置位置预测器
            if self.position_predictors['hundreds'] is not None:
                self.models['constraint'].set_position_predictors(
                    self.position_predictors['hundreds'],
                    self.position_predictors['tens'],
                    self.position_predictors['units']
                )

            # 为集成模型添加子模型
            ensemble_model = self.models['ensemble']
            for model_name in ['xgb', 'lgb', 'lstm', 'distribution', 'constraint']:
                ensemble_model.add_model(model_name, self.models[model_name])

            self.logger.info("所有和值预测模型构建完成")

        except Exception as e:
            self.logger.error(f"构建模型失败: {e}")
            raise

    def set_position_predictors(self, hundreds_predictor, tens_predictor, units_predictor):
        """设置位置预测器（用于约束优化）"""
        self.position_predictors['hundreds'] = hundreds_predictor
        self.position_predictors['tens'] = tens_predictor
        self.position_predictors['units'] = units_predictor

        # 如果约束模型已存在，更新其位置预测器
        if 'constraint' in self.models:
            self.models['constraint'].set_position_predictors(
                hundreds_predictor, tens_predictor, units_predictor
            )
    
    def train(self, model_name: Optional[str] = None, X: Optional[np.ndarray] = None,
              y: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        训练模型

        Args:
            model_name: 要训练的模型名称，None表示训练所有模型
            X: 特征矩阵，None表示从数据库加载
            y: 目标向量，None表示从数据库加载

        Returns:
            训练性能指标
        """
        try:
            if not self.models:
                self.build_model()

            # 如果指定了模型名称，只训练该模型
            if model_name is not None:
                if model_name not in self.models:
                    raise ValueError(f"未知的模型名称: {model_name}")

                performance = self.models[model_name].train(X, y)
                self.logger.info(f"模型 {model_name} 训练完成")
                return {model_name: performance}

            # 训练所有模型
            performances = {}
            for name, model in self.models.items():
                if name != 'ensemble':  # 集成模型最后训练
                    try:
                        performance = model.train(X, y)
                        performances[name] = performance
                        self.logger.info(f"模型 {name} 训练完成")
                    except Exception as e:
                        self.logger.error(f"模型 {name} 训练失败: {e}")
                        performances[name] = {'error': str(e)}

            # 最后训练集成模型
            try:
                ensemble_performance = self.models['ensemble'].train(X, y)
                performances['ensemble'] = ensemble_performance
                self.logger.info("集成模型训练完成")
            except Exception as e:
                self.logger.error(f"集成模型训练失败: {e}")
                performances['ensemble'] = {'error': str(e)}

            self.is_trained = True
            return performances

        except Exception as e:
            self.logger.error(f"训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray, model_name: Optional[str] = None) -> np.ndarray:
        """
        预测和值

        Args:
            X: 特征矩阵
            model_name: 使用的模型名称，None表示使用当前模型

        Returns:
            预测的和值数组
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")

        model_to_use = model_name or self.current_model

        if model_to_use not in self.models:
            raise ValueError(f"未知的模型名称: {model_to_use}")

        try:
            predictions = self.models[model_to_use].predict(X)
            return predictions
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise

    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """
        预测和值概率分布（适配BaseIndependentPredictor接口）

        Args:
            X: 特征矩阵

        Returns:
            概率分布数组，shape: (n_samples, 28) # 和值0-27
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")

        try:
            # 使用分布模型预测概率分布
            if 'distribution' in self.models and self.models['distribution'].is_trained:
                probabilities = self.models['distribution'].predict_distribution(X)
                return probabilities
            else:
                # 如果分布模型不可用，使用回归模型转换为概率
                predictions = self.predict(X)
                probabilities = self._convert_regression_to_probability(predictions)
                return probabilities
        except Exception as e:
            self.logger.error(f"预测概率分布失败: {e}")
            raise

    def _convert_regression_to_probability(self, predictions: np.ndarray) -> np.ndarray:
        """将回归预测转换为概率分布"""
        probabilities = []
        for pred in predictions:
            # 创建以预测值为中心的正态分布
            prob_dist = np.zeros(28)  # 和值0-27
            center = int(np.clip(pred, 0, 27))

            # 使用高斯分布
            for i in range(28):
                prob_dist[i] = np.exp(-0.5 * ((i - pred) / 2.0) ** 2)

            # 归一化
            prob_dist = prob_dist / np.sum(prob_dist)
            probabilities.append(prob_dist)

        return np.array(probabilities)

    def get_constraint_info(self, X: np.ndarray) -> Dict[str, Any]:
        """
        获取约束信息（新增接口，用于P8融合系统）

        Args:
            X: 特征矩阵

        Returns:
            约束信息字典
        """
        try:
            # 获取和值预测
            sum_predictions = self.predict(X)
            sum_probabilities = self.predict_probability(X)

            # 获取置信度
            _, confidences = self.predict_with_confidence(X)

            constraint_info = {
                'sum_predictions': sum_predictions.tolist(),
                'sum_probabilities': sum_probabilities.tolist(),
                'confidences': confidences.tolist(),
                'valid_range': self.prediction_range,
                'constraint_type': 'sum_constraint',
                'tolerance': self.config.get('constraint_tolerance', 2.0)
            }

            # 如果有约束模型，获取约束评分
            if 'constraint' in self.models and self.models['constraint'].is_trained:
                try:
                    constraint_scores = self.models['constraint'].evaluate_constraints(X)
                    constraint_info['constraint_scores'] = constraint_scores
                except:
                    pass

            return constraint_info

        except Exception as e:
            self.logger.error(f"获取约束信息失败: {e}")
            return {
                'sum_predictions': [],
                'sum_probabilities': [],
                'confidences': [],
                'valid_range': self.prediction_range,
                'constraint_type': 'sum_constraint',
                'error': str(e)
            }

    def predict_with_confidence(self, X: np.ndarray, model_name: Optional[str] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测和值及置信度

        Args:
            X: 特征矩阵
            model_name: 使用的模型名称

        Returns:
            预测值和置信度数组
        """
        model_to_use = model_name or self.current_model

        if model_to_use not in self.models:
            raise ValueError(f"未知的模型名称: {model_to_use}")

        try:
            if hasattr(self.models[model_to_use], 'predict_with_confidence'):
                return self.models[model_to_use].predict_with_confidence(X)
            else:
                predictions = self.models[model_to_use].predict(X)
                confidences = np.full(len(predictions), 0.5)  # 默认置信度
                return predictions, confidences
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise

    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray,
                 model_name: Optional[str] = None) -> Dict[str, float]:
        """
        评估模型性能

        Args:
            X_test: 测试特征
            y_test: 测试目标
            model_name: 要评估的模型名称

        Returns:
            性能指标字典
        """
        model_to_use = model_name or self.current_model

        if model_to_use not in self.models:
            raise ValueError(f"未知的模型名称: {model_to_use}")

        try:
            return self.models[model_to_use].evaluate(X_test, y_test)
        except Exception as e:
            self.logger.error(f"评估失败: {e}")
            raise

    def switch_model(self, model_name: str):
        """切换当前使用的模型"""
        if model_name not in self.models:
            raise ValueError(f"未知的模型名称: {model_name}")

        self.current_model = model_name
        self.logger.info(f"切换到模型: {model_name}")

    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        return list(self.models.keys())

    def save_model(self, model_name: Optional[str] = None, filepath: Optional[str] = None) -> bool:
        """
        保存模型

        Args:
            model_name: 要保存的模型名称，None表示保存所有模型
            filepath: 保存路径

        Returns:
            保存是否成功
        """
        try:
            if filepath is None:
                filepath = f"models/sum_predictor/{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            Path(filepath).mkdir(parents=True, exist_ok=True)

            if model_name is not None:
                # 保存指定模型
                if model_name not in self.models:
                    raise ValueError(f"未知的模型名称: {model_name}")

                model_path = Path(filepath) / f"{model_name}_model.pkl"
                return self.models[model_name].save_model(str(model_path))

            # 保存所有模型
            success_count = 0
            for name, model in self.models.items():
                model_path = Path(filepath) / f"{name}_model.pkl"
                if model.save_model(str(model_path)):
                    success_count += 1

            # 保存主预测器配置
            config_path = Path(filepath) / "sum_predictor_config.json"
            config_data = {
                'current_model': self.current_model,
                'prediction_range': self.prediction_range,
                'target_type': self.target_type,
                'available_models': list(self.models.keys())
            }

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"保存了 {success_count}/{len(self.models)} 个模型")
            return success_count == len(self.models)

        except Exception as e:
            self.logger.error(f"保存模型失败: {e}")
            return False

    def load_model(self, filepath: str, model_name: Optional[str] = None) -> bool:
        """
        加载模型

        Args:
            filepath: 模型文件路径
            model_name: 要加载的模型名称，None表示加载所有模型

        Returns:
            加载是否成功
        """
        try:
            if model_name is not None:
                # 加载指定模型
                if model_name not in self.models:
                    raise ValueError(f"未知的模型名称: {model_name}")

                model_path = Path(filepath) / f"{model_name}_model.pkl"
                return self.models[model_name].load_model(str(model_path))

            # 加载所有模型
            success_count = 0
            for name, model in self.models.items():
                model_path = Path(filepath) / f"{name}_model.pkl"
                if model_path.exists() and model.load_model(str(model_path)):
                    success_count += 1

            # 加载主预测器配置
            config_path = Path(filepath) / "sum_predictor_config.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                self.current_model = config_data.get('current_model', 'ensemble')
                self.prediction_range = tuple(config_data.get('prediction_range', (0, 27)))
                self.target_type = config_data.get('target_type', 'regression')

            self.is_trained = success_count > 0
            self.logger.info(f"加载了 {success_count}/{len(self.models)} 个模型")
            return success_count > 0

        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            return False

    def predict_next_period(self, issue: str, features: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        预测下一期和值

        Args:
            issue: 期号
            features: 特征数据，None表示自动构建

        Returns:
            预测结果字典
        """
        try:
            if features is None:
                # 从数据库构建特征
                features = self._build_features_for_prediction()

            # 使用当前模型预测
            predictions, confidences = self.predict_with_confidence(features)

            # 构建预测结果
            result = {
                'issue': issue,
                'model_type': self.current_model,
                'predicted_digit': float(predictions[0]),
                'confidence': float(confidences[0]),
                'prediction_range_min': max(0, int(predictions[0] - 2)),
                'prediction_range_max': min(27, int(predictions[0] + 2)),
                'created_at': datetime.now().isoformat()
            }

            # 如果是分布模型，添加概率分布
            if self.current_model == 'distribution':
                distribution_model = self.models['distribution']
                probabilities = distribution_model.predict_distribution(features)
                result['probabilities'] = probabilities[0].tolist()

                # 计算分布熵
                entropy = distribution_model.calculate_distribution_entropy(features)
                result['distribution_entropy'] = float(entropy[0])

            # 如果是约束模型，添加约束分数
            if self.current_model == 'constraint':
                constraint_model = self.models['constraint']
                position_pred = {
                    'hundreds_pred': 5,  # 默认值，实际应从位置预测器获取
                    'tens_pred': 5,
                    'units_pred': 5
                }
                constraint_score = constraint_model.calculate_constraint_score(
                    predictions[0], position_pred
                )
                result['constraint_score'] = float(constraint_score)

            # 保存预测结果
            self.data_access.save_prediction_result(result)

            return result

        except Exception as e:
            self.logger.error(f"预测下一期失败: {e}")
            raise

    def _build_features_for_prediction(self) -> np.ndarray:
        """构建用于预测的特征"""
        # 这里应该实现特征构建逻辑
        # 暂时返回示例特征
        return np.array([[13.5] * 10])  # 示例：10个特征，都是13.5

    def get_model_performance_summary(self) -> Dict[str, Dict]:
        """获取所有模型的性能摘要"""
        summary = {}

        for model_name in self.models:
            try:
                # 从数据库获取性能历史
                performance_history = self.data_access.get_performance_history(model_name)
                if performance_history:
                    latest_performance = performance_history[0]
                    summary[model_name] = {
                        'latest_accuracy': latest_performance.get('accuracy', 0),
                        'latest_mae': latest_performance.get('mae', 0),
                        'latest_rmse': latest_performance.get('rmse', 0),
                        'evaluation_count': len(performance_history)
                    }
                else:
                    summary[model_name] = {'status': 'no_performance_data'}
            except Exception as e:
                summary[model_name] = {'error': str(e)}

        return summary

    def compare_models(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Dict]:
        """
        比较所有模型的性能

        Args:
            X_test: 测试特征
            y_test: 测试目标

        Returns:
            模型比较结果
        """
        comparison = {}

        for model_name, model in self.models.items():
            if model.is_trained:
                try:
                    performance = model.evaluate(X_test, y_test)
                    comparison[model_name] = performance
                except Exception as e:
                    comparison[model_name] = {'error': str(e)}
            else:
                comparison[model_name] = {'status': 'not_trained'}

        return comparison

    def get_best_model(self, metric: str = 'accuracy') -> str:
        """
        根据指定指标获取最佳模型

        Args:
            metric: 评估指标名称

        Returns:
            最佳模型名称
        """
        performance_summary = self.get_model_performance_summary()

        best_model = None
        best_score = -float('inf') if metric in ['accuracy', 'r2_score'] else float('inf')

        for model_name, perf in performance_summary.items():
            if f'latest_{metric}' in perf:
                score = perf[f'latest_{metric}']

                if metric in ['accuracy', 'r2_score']:
                    # 越大越好
                    if score > best_score:
                        best_score = score
                        best_model = model_name
                else:
                    # 越小越好（如mae, rmse）
                    if score < best_score:
                        best_score = score
                        best_model = model_name

        return best_model or 'ensemble'

    def auto_select_model(self) -> str:
        """自动选择最佳模型"""
        best_model = self.get_best_model('accuracy')
        self.switch_model(best_model)
        return best_model

    def get_model_weights(self) -> Dict[str, float]:
        """获取集成模型的权重"""
        if 'ensemble' in self.models:
            return self.models['ensemble'].model_weights
        return {}

    def set_model_weights(self, weights: Dict[str, float]):
        """设置集成模型的权重"""
        if 'ensemble' in self.models:
            self.models['ensemble'].set_model_weights(weights)
            self.logger.info(f"更新集成模型权重: {weights}")

    def enable_dynamic_weights(self, enable: bool = True):
        """启用动态权重调整"""
        if 'ensemble' in self.models:
            self.models['ensemble'].enable_dynamic_weights(enable)
            self.logger.info(f"动态权重调整: {'启用' if enable else '禁用'}")

    def get_model_contributions(self, X: np.ndarray) -> Dict[str, Any]:
        """获取各模型对预测的贡献"""
        if 'ensemble' in self.models and self.models['ensemble'].is_trained:
            return self.models['ensemble'].get_model_contributions(X)
        return {}

    def batch_predict(self, X_list: List[np.ndarray], model_name: Optional[str] = None) -> List[np.ndarray]:
        """
        批量预测

        Args:
            X_list: 特征矩阵列表
            model_name: 使用的模型名称

        Returns:
            预测结果列表
        """
        results = []
        for X in X_list:
            predictions = self.predict(X, model_name)
            results.append(predictions)
        return results

    def cross_validate(self, X: np.ndarray, y: np.ndarray, cv_folds: int = 5) -> Dict[str, Dict]:
        """
        交叉验证所有模型

        Args:
            X: 特征矩阵
            y: 目标向量
            cv_folds: 交叉验证折数

        Returns:
            交叉验证结果
        """
        from sklearn.model_selection import KFold

        kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
        cv_results = {}

        for model_name, model in self.models.items():
            if model_name == 'ensemble':
                continue  # 跳过集成模型

            fold_scores = []

            for train_idx, val_idx in kf.split(X):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]

                try:
                    # 训练模型
                    model.train(X_train, y_train)

                    # 评估模型
                    performance = model.evaluate(X_val, y_val)
                    fold_scores.append(performance)

                except Exception as e:
                    self.logger.warning(f"模型 {model_name} 第{len(fold_scores)+1}折验证失败: {e}")

            # 计算平均性能
            if fold_scores:
                avg_performance = {}
                for metric in fold_scores[0].keys():
                    scores = [score[metric] for score in fold_scores if metric in score]
                    if scores:
                        avg_performance[f'avg_{metric}'] = np.mean(scores)
                        avg_performance[f'std_{metric}'] = np.std(scores)

                cv_results[model_name] = avg_performance
            else:
                cv_results[model_name] = {'error': 'no_valid_folds'}

        return cv_results

    # ==================== 约束优化功能 ====================

    def predict_with_position_constraints(self, X: np.ndarray,
                                        position_predictions: Dict[str, np.ndarray]) -> Tuple[np.ndarray, Dict]:
        """
        基于位置预测的约束优化预测

        Args:
            X: 特征矩阵
            position_predictions: 位置预测结果

        Returns:
            优化后的预测结果和约束信息
        """
        # 获取基础预测
        base_predictions = self.predict(X)

        # 计算期望和值（基于位置预测）
        expected_sums = self._calculate_expected_sums(position_predictions)

        # 约束优化
        optimized_predictions = []
        constraint_info = []

        for i in range(len(base_predictions)):
            # 获取当前样本的位置预测
            h_pred = position_predictions.get('hundreds_pred', [5])[i] if len(position_predictions.get('hundreds_pred', [5])) > i else 5
            t_pred = position_predictions.get('tens_pred', [5])[i] if len(position_predictions.get('tens_pred', [5])) > i else 5
            u_pred = position_predictions.get('units_pred', [5])[i] if len(position_predictions.get('units_pred', [5])) > i else 5

            # 计算约束一致性
            position_sum = h_pred + t_pred + u_pred
            base_pred = base_predictions[i]
            expected_sum = expected_sums[i] if i < len(expected_sums) else 13.5

            # 约束优化算法
            weight_base = 0.6
            weight_position = 0.3
            weight_expected = 0.1

            optimized_pred = (weight_base * base_pred +
                            weight_position * position_sum +
                            weight_expected * expected_sum)

            # 应用范围约束
            optimized_pred = np.clip(optimized_pred, 0, 27)
            optimized_predictions.append(optimized_pred)

            # 记录约束信息
            constraint_info.append({
                'base_prediction': float(base_pred),
                'position_sum': float(position_sum),
                'expected_sum': float(expected_sum),
                'optimized_prediction': float(optimized_pred),
                'constraint_score': self._calculate_constraint_consistency(optimized_pred, position_sum),
                'optimization_weights': {
                    'base': weight_base,
                    'position': weight_position,
                    'expected': weight_expected
                }
            })

        return np.array(optimized_predictions), {'constraint_details': constraint_info}

    def _calculate_expected_sums(self, position_predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """计算基于位置概率的期望和值"""
        hundreds_prob = position_predictions.get('hundreds_prob')
        tens_prob = position_predictions.get('tens_prob')
        units_prob = position_predictions.get('units_prob')

        if hundreds_prob is None or tens_prob is None or units_prob is None:
            # 如果没有概率分布，使用点预测
            h_pred = position_predictions.get('hundreds_pred', np.array([5]))
            t_pred = position_predictions.get('tens_pred', np.array([5]))
            u_pred = position_predictions.get('units_pred', np.array([5]))
            return h_pred + t_pred + u_pred

        expected_sums = []
        sample_count = len(hundreds_prob)

        for i in range(sample_count):
            expected_sum = 0
            for h in range(10):
                for t in range(10):
                    for u in range(10):
                        combination_sum = h + t + u
                        combination_prob = (hundreds_prob[i][h] *
                                          tens_prob[i][t] *
                                          units_prob[i][u])
                        expected_sum += combination_sum * combination_prob
            expected_sums.append(expected_sum)

        return np.array(expected_sums)

    def _calculate_constraint_consistency(self, predicted_sum: float, position_sum: float) -> float:
        """计算约束一致性分数"""
        max_diff = 27  # 最大可能差异
        actual_diff = abs(predicted_sum - position_sum)
        consistency_score = max(0, 1 - actual_diff / max_diff)
        return consistency_score

    def evaluate_constraint_consistency(self, X_test: np.ndarray, y_test: np.ndarray,
                                      position_predictions: Dict[str, np.ndarray]) -> Dict[str, float]:
        """
        评估约束一致性

        Args:
            X_test: 测试特征
            y_test: 测试目标
            position_predictions: 位置预测结果

        Returns:
            约束一致性评估结果
        """
        # 获取约束优化预测
        optimized_preds, constraint_info = self.predict_with_position_constraints(X_test, position_predictions)

        # 计算基础性能指标
        mae = np.mean(np.abs(y_test - optimized_preds))
        rmse = np.sqrt(np.mean((y_test - optimized_preds) ** 2))
        accuracy_1 = np.mean(np.abs(y_test - optimized_preds) <= 1)
        accuracy_2 = np.mean(np.abs(y_test - optimized_preds) <= 2)

        # 计算约束一致性指标
        constraint_scores = [info['constraint_score'] for info in constraint_info['constraint_details']]
        avg_constraint_score = np.mean(constraint_scores)

        # 计算改进程度（与基础预测比较）
        base_preds = self.predict(X_test)
        base_mae = np.mean(np.abs(y_test - base_preds))
        improvement = (base_mae - mae) / base_mae if base_mae > 0 else 0

        return {
            'mae': mae,
            'rmse': rmse,
            'accuracy_1': accuracy_1,
            'accuracy_2': accuracy_2,
            'avg_constraint_score': avg_constraint_score,
            'improvement_over_base': improvement,
            'constraint_consistency_rate': np.mean(np.array(constraint_scores) > 0.7)  # 一致性良好的比例
        }

    def optimize_ensemble_weights_with_constraints(self, X_val: np.ndarray, y_val: np.ndarray,
                                                 position_predictions: Dict[str, np.ndarray]) -> Dict[str, float]:
        """
        基于约束一致性优化集成权重

        Args:
            X_val: 验证特征
            y_val: 验证目标
            position_predictions: 位置预测结果

        Returns:
            优化后的权重
        """
        if 'ensemble' not in self.models:
            return {}

        # 获取各模型的预测和约束一致性
        model_scores = {}

        for model_name, model in self.models.items():
            if model_name == 'ensemble' or not model.is_trained:
                continue

            try:
                # 切换到当前模型进行预测
                original_model = self.current_model
                self.switch_model(model_name)

                # 评估约束一致性
                constraint_eval = self.evaluate_constraint_consistency(X_val, y_val, position_predictions)

                # 综合评分（准确率 + 约束一致性）
                accuracy_score = constraint_eval['accuracy_1']
                constraint_score = constraint_eval['avg_constraint_score']
                combined_score = 0.7 * accuracy_score + 0.3 * constraint_score

                model_scores[model_name] = combined_score

                # 恢复原模型
                self.switch_model(original_model)

            except Exception as e:
                self.logger.warning(f"评估模型 {model_name} 约束一致性失败: {e}")
                model_scores[model_name] = 0

        # 基于评分计算权重
        if model_scores:
            total_score = sum(model_scores.values())
            if total_score > 0:
                optimized_weights = {name: score/total_score for name, score in model_scores.items()}

                # 设置集成模型权重
                self.set_model_weights(optimized_weights)

                self.logger.info(f"基于约束一致性优化权重: {optimized_weights}")
                return optimized_weights

        return self.get_model_weights()
