#!/usr/bin/env python3
"""
P8智能交集融合系统命令行工具

提供完整的命令行接口，方便用户使用P8融合系统

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import argparse
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

from src.fusion.fusion_predictor import FusionPredictor
from src.fusion.performance_monitor import PerformanceMonitor
from src.fusion.report_generator import ReportGenerator
from src.fusion.auto_adjustment_trigger import AutoAdjustmentTrigger
from src.data.fusion_data_access import FusionDataAccess

class P8FusionCLI:
    """P8融合系统命令行接口"""
    
    def __init__(self):
        self.fusion_predictor = None
        self.performance_monitor = None
        self.report_generator = None
        self.auto_trigger = None
        
        # 默认配置
        self.default_db_path = "data/lottery.db"
        self.default_config_path = "config/fusion_config.yaml"
    
    def create_parser(self):
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="P8智能交集融合系统命令行工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  # 预测下一期号码
  python p8_fusion_cli.py predict --issue 2024100
  
  # 评估预测结果
  python p8_fusion_cli.py evaluate --issue 2024099 --actual 123
  
  # 生成性能报告
  python p8_fusion_cli.py report --days 30
  
  # 启动性能监控
  python p8_fusion_cli.py monitor --start
  
  # 查看系统状态
  python p8_fusion_cli.py status
            """
        )
        
        # 全局参数
        parser.add_argument('--db', default=self.default_db_path,
                          help='数据库路径 (默认: data/lottery.db)')
        parser.add_argument('--config', default=self.default_config_path,
                          help='配置文件路径 (默认: config/fusion_config.yaml)')
        parser.add_argument('--verbose', '-v', action='store_true',
                          help='详细输出')
        
        # 子命令
        subparsers = parser.add_subparsers(dest='command', help='可用命令')
        
        # 预测命令
        predict_parser = subparsers.add_parser('predict', help='预测下一期号码')
        predict_parser.add_argument('--issue', required=True, help='期号')
        predict_parser.add_argument('--method', choices=['adaptive_fusion', 'weighted_average', 'bayesian_fusion'],
                                  default='adaptive_fusion', help='融合方法')
        predict_parser.add_argument('--strategy', choices=['adaptive', 'probability_first', 'constraint_first'],
                                  default='adaptive', help='排序策略')
        predict_parser.add_argument('--top-k', type=int, default=10, help='返回前K个结果')
        predict_parser.add_argument('--output', help='输出文件路径')
        
        # 评估命令
        evaluate_parser = subparsers.add_parser('evaluate', help='评估预测结果')
        evaluate_parser.add_argument('--issue', required=True, help='期号')
        evaluate_parser.add_argument('--actual', required=True, help='实际开奖结果 (如: 123)')
        evaluate_parser.add_argument('--update-weights', action='store_true', help='更新权重')
        
        # 报告命令
        report_parser = subparsers.add_parser('report', help='生成性能报告')
        report_parser.add_argument('--days', type=int, default=30, help='统计天数')
        report_parser.add_argument('--output-dir', default='reports', help='报告输出目录')
        report_parser.add_argument('--no-charts', action='store_true', help='不生成图表')
        
        # 监控命令
        monitor_parser = subparsers.add_parser('monitor', help='性能监控')
        monitor_group = monitor_parser.add_mutually_exclusive_group(required=True)
        monitor_group.add_argument('--start', action='store_true', help='启动监控')
        monitor_group.add_argument('--stop', action='store_true', help='停止监控')
        monitor_group.add_argument('--status', action='store_true', help='监控状态')
        monitor_parser.add_argument('--interval', type=int, default=60, help='监控间隔(秒)')
        
        # 状态命令
        status_parser = subparsers.add_parser('status', help='查看系统状态')
        status_parser.add_argument('--json', action='store_true', help='JSON格式输出')
        
        # 权重命令
        weights_parser = subparsers.add_parser('weights', help='权重管理')
        weights_group = weights_parser.add_mutually_exclusive_group(required=True)
        weights_group.add_argument('--show', action='store_true', help='显示当前权重')
        weights_group.add_argument('--reset', action='store_true', help='重置权重')
        weights_group.add_argument('--optimize', action='store_true', help='优化权重')
        
        # 测试命令
        test_parser = subparsers.add_parser('test', help='运行测试')
        test_group = test_parser.add_mutually_exclusive_group(required=True)
        test_group.add_argument('--integration', action='store_true', help='集成测试')
        test_group.add_argument('--benchmark', action='store_true', help='性能基准测试')
        test_group.add_argument('--validate', action='store_true', help='验证系统')
        
        return parser
    
    def initialize_system(self, db_path: str, config_path: str):
        """初始化系统"""
        try:
            print("正在初始化P8智能交集融合系统...")
            
            # 检查文件存在性
            if not Path(db_path).exists():
                print(f"错误: 数据库文件不存在: {db_path}")
                return False
            
            # 初始化融合预测器
            self.fusion_predictor = FusionPredictor(db_path, config_path)
            
            # 初始化其他组件
            self.report_generator = ReportGenerator(db_path)
            
            print("✓ 系统初始化完成")
            return True
            
        except Exception as e:
            print(f"错误: 系统初始化失败: {e}")
            return False
    
    def cmd_predict(self, args):
        """预测命令"""
        print(f"开始预测期号: {args.issue}")
        
        try:
            # 验证输入
            validation = self.fusion_predictor.validate_prediction_input(args.issue)
            if not validation['valid']:
                print("输入验证失败:")
                for error in validation['errors']:
                    print(f"  - {error}")
                return False
            
            if validation['warnings']:
                print("警告:")
                for warning in validation['warnings']:
                    print(f"  - {warning}")
            
            # 执行预测
            start_time = time.time()
            result = self.fusion_predictor.predict_next_period(
                args.issue, args.method, args.strategy, args.top_k
            )
            execution_time = time.time() - start_time
            
            # 输出结果
            print(f"\n预测完成! 执行时间: {execution_time:.2f}秒")
            print(f"融合方法: {result['fusion_method']}")
            print(f"排序策略: {result['ranking_strategy']}")
            print(f"生成组合数: {result['total_combinations']}")
            
            print(f"\nTop-{args.top_k} 预测结果:")
            print("-" * 80)
            print(f"{'排名':<4} {'组合':<6} {'概率':<8} {'约束分数':<8} {'多样性':<8} {'置信度':<8}")
            print("-" * 80)
            
            for pred in result['predictions']:
                print(f"{pred['rank']:<4} {pred['combination']:<6} "
                     f"{pred['combined_probability']:<8.4f} "
                     f"{pred['constraint_score']:<8.3f} "
                     f"{pred['diversity_score']:<8.3f} "
                     f"{pred['confidence_level']:<8}")
            
            # 保存到文件
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False, default=str)
                print(f"\n结果已保存到: {args.output}")
            
            return True
            
        except Exception as e:
            print(f"错误: 预测失败: {e}")
            return False
    
    def cmd_evaluate(self, args):
        """评估命令"""
        print(f"评估期号 {args.issue} 的预测结果，实际结果: {args.actual}")
        
        try:
            # 执行评估
            result = self.fusion_predictor.evaluate_and_update_weights(args.actual, args.issue)
            
            if 'error' in result:
                print(f"评估失败: {result['error']}")
                return False
            
            # 输出评估结果
            performance = result['performance_summary']
            print(f"\n评估结果:")
            print(f"  总评估数: {performance.get('total_evaluations', 0)}")
            print(f"  精确命中率: {performance.get('exact_hit_rate', 0):.2%}")
            print(f"  Top-10命中率: {performance.get('top_10_hit_rate', 0):.2%}")
            print(f"  平均综合评分: {performance.get('avg_overall_score', 0):.3f}")
            
            if args.update_weights:
                print(f"\n权重更新:")
                for name, change in result['weight_changes'].items():
                    print(f"  {name}: {change['old_weight']:.3f} → {change['new_weight']:.3f} "
                         f"({change['relative_change']:+.1%})")
            
            return True
            
        except Exception as e:
            print(f"错误: 评估失败: {e}")
            return False
    
    def cmd_report(self, args):
        """报告命令"""
        print(f"生成{args.days}天的性能报告...")
        
        try:
            result = self.report_generator.generate_performance_report(
                days=args.days,
                include_charts=not args.no_charts
            )
            
            if 'error' in result:
                print(f"报告生成失败: {result['error']}")
                return False
            
            print(f"✓ HTML报告: {result['html_file']}")
            print(f"✓ JSON数据: {result['json_file']}")
            
            # 显示摘要
            data = result['report_data']
            summary = data['summary']
            performance = data['performance']
            
            print(f"\n报告摘要:")
            print(f"  统计周期: {args.days}天")
            print(f"  总预测数: {summary['total_predictions']}")
            print(f"  成功率: {summary['success_rate']:.2%}")
            print(f"  精确命中率: {performance['hit_rates']['exact_hit_rate']:.2%}")
            print(f"  Top-K命中率: {performance['hit_rates']['top_k_hit_rate']:.2%}")
            
            return True
            
        except Exception as e:
            print(f"错误: 报告生成失败: {e}")
            return False
    
    def cmd_monitor(self, args):
        """监控命令"""
        if args.start:
            print("启动性能监控...")
            try:
                if not self.performance_monitor:
                    self.performance_monitor = PerformanceMonitor(
                        self.fusion_predictor.db_path,
                        self.fusion_predictor.config.get('monitoring', {})
                    )
                
                self.performance_monitor.start_monitoring(args.interval)
                print(f"✓ 性能监控已启动，监控间隔: {args.interval}秒")
                print("按 Ctrl+C 停止监控")
                
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    self.performance_monitor.stop_monitoring()
                    print("\n✓ 性能监控已停止")
                
                return True
                
            except Exception as e:
                print(f"错误: 启动监控失败: {e}")
                return False
        
        elif args.status:
            print("监控状态:")
            if self.performance_monitor:
                summary = self.performance_monitor.get_monitoring_summary()
                print(f"  状态: {'运行中' if summary['monitoring_status'] else '已停止'}")
                print(f"  运行时间: {summary['uptime_hours']:.1f}小时")
                print(f"  指标数量: {summary['total_metrics']}")
                print(f"  告警规则: {summary['active_alerts']}/{summary['alert_rules_count']}")
            else:
                print("  状态: 未初始化")
            
            return True
    
    def cmd_status(self, args):
        """状态命令"""
        try:
            status = self.fusion_predictor.get_system_status()
            
            if args.json:
                print(json.dumps(status, indent=2, ensure_ascii=False, default=str))
            else:
                print("P8智能交集融合系统状态:")
                print(f"  系统初始化: {'✓' if status['system_initialized'] else '✗'}")
                print(f"  预测次数: {status['prediction_count']}")
                print(f"  最后预测: {status['last_prediction_time'] or '无'}")
                
                print(f"\n预测器状态:")
                predictor_status = status.get('predictor_status', {})
                for name, pred_status in predictor_status.items():
                    loaded = '✓' if pred_status.get('loaded', False) else '✗'
                    trained = '✓' if pred_status.get('trained', False) else '✗'
                    print(f"  {name}: 加载{loaded} 训练{trained}")
                
                print(f"\n性能摘要:")
                perf = status.get('performance_summary', {})
                print(f"  总评估数: {perf.get('total_evaluations', 0)}")
                print(f"  精确命中率: {perf.get('exact_hit_rate', 0):.2%}")
                print(f"  Top-10命中率: {perf.get('top_10_hit_rate', 0):.2%}")
            
            return True
            
        except Exception as e:
            print(f"错误: 获取状态失败: {e}")
            return False
    
    def cmd_weights(self, args):
        """权重命令"""
        try:
            if args.show:
                weights = self.fusion_predictor.data_access.get_fusion_weights()
                print("当前权重配置:")
                for name, weight in weights.items():
                    print(f"  {name}: {weight:.3f}")
            
            elif args.reset:
                print("重置权重到默认值...")
                self.fusion_predictor.weight_adjuster.reset_weights()
                print("✓ 权重已重置")
            
            elif args.optimize:
                print("优化融合参数...")
                result = self.fusion_predictor.optimize_fusion_parameters()
                if 'error' in result:
                    print(f"优化失败: {result['error']}")
                    return False
                
                print("✓ 参数优化完成")
                improvements = result.get('improvements', {})
                if improvements:
                    print("优化项目:")
                    for param, reason in improvements.items():
                        print(f"  {param}: {reason}")
            
            return True
            
        except Exception as e:
            print(f"错误: 权重操作失败: {e}")
            return False
    
    def cmd_test(self, args):
        """测试命令"""
        try:
            if args.integration:
                print("运行集成测试...")
                from tests.test_p8_integration import run_integration_tests
                success = run_integration_tests()
                print(f"集成测试{'通过' if success else '失败'}")
                return success
            
            elif args.benchmark:
                print("运行性能基准测试...")
                from tests.test_p8_benchmark import run_benchmark_tests
                success = run_benchmark_tests()
                print(f"基准测试{'通过' if success else '失败'}")
                return success
            
            elif args.validate:
                print("验证系统配置...")
                # 简单的系统验证
                validation_result = self.fusion_predictor.validate_prediction_input('test')
                print(f"系统验证: {'通过' if validation_result['valid'] else '失败'}")
                
                if not validation_result['valid']:
                    for error in validation_result['errors']:
                        print(f"  错误: {error}")
                
                return validation_result['valid']
            
        except Exception as e:
            print(f"错误: 测试失败: {e}")
            return False
    
    def run(self):
        """运行命令行工具"""
        parser = self.create_parser()
        args = parser.parse_args()
        
        if not args.command:
            parser.print_help()
            return False
        
        # 初始化系统
        if not self.initialize_system(args.db, args.config):
            return False
        
        # 执行命令
        command_map = {
            'predict': self.cmd_predict,
            'evaluate': self.cmd_evaluate,
            'report': self.cmd_report,
            'monitor': self.cmd_monitor,
            'status': self.cmd_status,
            'weights': self.cmd_weights,
            'test': self.cmd_test
        }
        
        if args.command in command_map:
            return command_map[args.command](args)
        else:
            print(f"错误: 未知命令: {args.command}")
            return False

def main():
    """主函数"""
    cli = P8FusionCLI()
    success = cli.run()
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
