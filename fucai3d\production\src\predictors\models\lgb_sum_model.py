#!/usr/bin/env python3
"""
LightGBM和值预测模型

实现基于LightGBM的和值回归预测模型
支持快速训练和预测，与XGBSumModel类似的接口和功能

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import numpy as np
import pandas as pd
import pickle
import logging
from typing import Dict, List, Tuple, Optional, Any
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import sqlite3
import json
from pathlib import Path

class LGBSumModel:
    """LightGBM和值预测模型"""
    
    def __init__(self, db_path: str, config: Optional[Dict] = None):
        """
        初始化LightGBM和值模型
        
        Args:
            db_path: 数据库路径
            config: 模型配置参数
        """
        self.db_path = db_path
        self.model = None
        self.is_trained = False
        self.feature_names = None
        self.logger = logging.getLogger("LGBSumModel")
        
        # 默认模型参数
        self.model_params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.1,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'n_estimators': 200,
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1
        }
        
        # 更新配置参数
        if config and 'lgb' in config:
            self.model_params.update(config['lgb'])
    
    def build_model(self):
        """构建LightGBM回归模型"""
        try:
            import lightgbm as lgb
            self.model = lgb.LGBMRegressor(**self.model_params)
            self.logger.info("LightGBM和值模型构建成功")
            return self.model
        except ImportError:
            error_msg = "LightGBM未安装，请安装: pip install lightgbm"
            self.logger.error(error_msg)
            raise ImportError(error_msg)
    
    def prepare_features(self, data: pd.DataFrame, window_size: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备训练特征
        
        Args:
            data: 包含彩票数据的DataFrame
            window_size: 滑动窗口大小
            
        Returns:
            特征矩阵X和目标向量y
        """
        # 计算和值
        data['sum_value'] = data['hundreds'] + data['tens'] + data['units']
        
        X = []
        y = []
        
        # 构建滑动窗口特征
        for i in range(window_size, len(data)):
            # 基础特征：前window_size期的和值
            features = []
            
            # 历史和值
            for j in range(i - window_size, i):
                features.append(data.iloc[j]['sum_value'])
            
            # 统计特征
            recent_sums = data.iloc[i - window_size:i]['sum_value']
            features.extend([
                recent_sums.mean(),      # 平均值
                recent_sums.std(),       # 标准差
                recent_sums.min(),       # 最小值
                recent_sums.max(),       # 最大值
                recent_sums.median(),    # 中位数
            ])
            
            # 趋势特征
            if len(recent_sums) >= 3:
                # 简单线性趋势
                x_vals = np.arange(len(recent_sums))
                trend = np.polyfit(x_vals, recent_sums, 1)[0]
                features.append(trend)
            else:
                features.append(0)
            
            # LightGBM特有特征：分位数
            features.extend([
                recent_sums.quantile(0.25),  # 25%分位数
                recent_sums.quantile(0.75),  # 75%分位数
            ])
            
            X.append(features)
            y.append(data.iloc[i]['sum_value'])
        
        # 设置特征名称
        self.feature_names = []
        for j in range(window_size):
            self.feature_names.append(f'sum_lag_{j+1}')
        self.feature_names.extend([
            'sum_mean', 'sum_std', 'sum_min', 'sum_max', 'sum_median', 
            'sum_trend', 'sum_q25', 'sum_q75'
        ])
        
        return np.array(X), np.array(y)
    
    def load_data(self) -> Tuple[np.ndarray, np.ndarray]:
        """从数据库加载训练数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 查询彩票数据
            query = """
                SELECT hundreds, tens, units, issue, draw_date
                FROM lottery_data
                ORDER BY draw_date, issue
            """
            
            data = pd.read_sql_query(query, conn)
            conn.close()
            
            if data.empty:
                raise ValueError("没有找到彩票数据")
            
            self.logger.info(f"加载了 {len(data)} 条彩票数据")
            
            # 准备特征
            X, y = self.prepare_features(data)
            
            self.logger.info(f"生成了 {len(X)} 个训练样本，特征维度: {X.shape[1]}")
            
            return X, y
            
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            raise
    
    def train(self, X: Optional[np.ndarray] = None, y: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        训练LightGBM模型
        
        Args:
            X: 特征矩阵，如果为None则从数据库加载
            y: 目标向量，如果为None则从数据库加载
            
        Returns:
            训练性能指标
        """
        try:
            # 如果没有提供数据，从数据库加载
            if X is None or y is None:
                X, y = self.load_data()
            
            # 构建模型
            if self.model is None:
                self.build_model()
            
            # 分割训练集和验证集
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            self.logger.info(f"训练集大小: {len(X_train)}, 验证集大小: {len(X_val)}")
            
            # 训练模型
            self.model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                callbacks=[
                    # LightGBM的早停回调
                    __import__('lightgbm').early_stopping(20),
                    __import__('lightgbm').log_evaluation(0)  # 不显示训练日志
                ]
            )
            
            self.is_trained = True
            
            # 评估模型
            performance = self.evaluate(X_val, y_val)
            
            self.logger.info(f"LightGBM和值模型训练完成，验证集性能: {performance}")
            
            return performance
            
        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            raise
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        预测和值
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测的和值数组
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            predictions = self.model.predict(X)
            
            # 应用约束：确保预测值在[0,27]范围内
            predictions = np.clip(predictions, 0, 27)
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise
    
    def predict_with_confidence(self, X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        预测和值及置信度
        
        Args:
            X: 特征矩阵
            
        Returns:
            预测值和置信度数组
        """
        predictions = self.predict(X)
        
        # 基于特征重要性的置信度计算
        confidences = []
        for pred in predictions:
            # 基于预测值与理论中位数(13.5)的距离计算置信度
            distance = abs(pred - 13.5)
            confidence = max(0.1, min(0.9, 1.0 - distance / 13.5))
            confidences.append(confidence)
        
        return predictions, np.array(confidences)
    
    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            X_test: 测试特征
            y_test: 测试目标
            
        Returns:
            性能指标字典
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            y_pred = self.predict(X_test)
            
            # 计算各种误差指标
            mae = mean_absolute_error(y_test, y_pred)
            rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            r2 = r2_score(y_test, y_pred)
            
            # 计算不同精度的准确率
            accuracy_1 = np.mean(np.abs(y_test - y_pred) <= 1)
            accuracy_2 = np.mean(np.abs(y_test - y_pred) <= 2)
            
            return {
                'mae': mae,
                'rmse': rmse,
                'r2_score': r2,
                'accuracy_1': accuracy_1,
                'accuracy_2': accuracy_2,
                'accuracy': accuracy_1  # 主要准确率指标
            }
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            raise
    
    def get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        try:
            importance = self.model.feature_importances_
            if self.feature_names:
                return dict(zip(self.feature_names, importance))
            else:
                return {f'feature_{i}': imp for i, imp in enumerate(importance)}
        except Exception as e:
            self.logger.error(f"获取特征重要性失败: {e}")
            return {}
    
    def save_model(self, filepath: str) -> bool:
        """
        保存模型
        
        Args:
            filepath: 保存路径
            
        Returns:
            保存是否成功
        """
        if not self.is_trained:
            self.logger.warning("模型尚未训练，无法保存")
            return False
        
        try:
            # 确保目录存在
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存模型和元数据
            model_data = {
                'model': self.model,
                'model_params': self.model_params,
                'feature_names': self.feature_names,
                'is_trained': self.is_trained
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"模型保存成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            return False
    
    def load_model(self, filepath: str) -> bool:
        """
        加载模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            加载是否成功
        """
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.model_params = model_data['model_params']
            self.feature_names = model_data['feature_names']
            self.is_trained = model_data['is_trained']
            
            self.logger.info(f"模型加载成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            return False
