"""
数据解析器模块
专门处理13字段数据解析，支持试机号码、销售额、机器号等新字段
"""

import re
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from bs4 import BeautifulSoup, Tag

logger = logging.getLogger(__name__)


class LotteryDataParser:
    """福彩3D数据解析器 - 支持13字段完整模型"""
    
    def __init__(self):
        self.field_patterns = self._init_field_patterns()
        self.date_formats = [
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}/\d{2}/\d{2}',
            r'\d{4}\.\d{2}\.\d{2}',
            r'\d{2}-\d{2}-\d{2}',
            r'\d{8}'
        ]
    
    def _init_field_patterns(self) -> Dict[str, str]:
        """初始化字段匹配模式"""
        return {
            'issue': r'(\d{7})',  # 期号：7位数字
            'date': r'(\d{4}[-/\.]\d{2}[-/\.]\d{2})',  # 日期
            'numbers': r'(\d)\s*(\d)\s*(\d)',  # 开奖号码：三位数字
            'trial_numbers': r'试机.*?(\d)\s*(\d)\s*(\d)',  # 试机号码
            'machine': r'[机器|设备].*?([A-Z]\d+|\d+)',  # 机器号
            'sales': r'销售.*?([\d,]+\.?\d*)',  # 销售额
            'prize': r'[奖金|奖池].*?([\d,]+\.?\d*)',  # 奖金信息
        }
    
    def parse_html_content(self, html_content: str) -> List[Dict[str, Any]]:
        """解析HTML内容"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            data_list = []
            
            # 查找数据表格
            tables = self._find_data_tables(soup)
            
            for table in tables:
                rows = table.find_all('tr')
                header_row = self._identify_header_row(rows)
                
                for row in rows:
                    if row == header_row:
                        continue
                    
                    try:
                        data = self._parse_table_row(row)
                        if data and self._validate_basic_data(data):
                            # 计算衍生字段
                            data = self._calculate_derived_fields(data)
                            data_list.append(data)
                    except Exception as e:
                        logger.warning(f"解析表格行失败: {str(e)}")
                        continue
            
            logger.info(f"HTML解析完成，获得 {len(data_list)} 条记录")
            return data_list
            
        except Exception as e:
            logger.error(f"HTML内容解析失败: {str(e)}")
            return []
    
    def _find_data_tables(self, soup: BeautifulSoup) -> List[Tag]:
        """查找包含数据的表格"""
        tables = []
        
        # 按优先级查找表格
        selectors = [
            'table.chart_table',  # 特定类名
            'table[class*="data"]',  # 包含data的类名
            'table[class*="lottery"]',  # 包含lottery的类名
            'table'  # 所有表格
        ]
        
        for selector in selectors:
            found_tables = soup.select(selector)
            for table in found_tables:
                if self._is_lottery_table(table):
                    tables.append(table)
            
            if tables:  # 如果找到了，就不继续查找
                break
        
        return tables
    
    def _is_lottery_table(self, table: Tag) -> bool:
        """判断是否为福彩数据表格"""
        table_text = table.get_text().lower()
        indicators = ['期号', '开奖', 'lottery', '3d', '福彩', '号码']
        return any(indicator in table_text for indicator in indicators)
    
    def _identify_header_row(self, rows: List[Tag]) -> Optional[Tag]:
        """识别表头行"""
        for row in rows[:3]:  # 通常表头在前3行
            row_text = row.get_text().lower()
            if any(header in row_text for header in ['期号', '开奖', 'issue', 'number']):
                return row
        return None
    
    def _parse_table_row(self, row: Tag) -> Optional[Dict[str, Any]]:
        """解析表格行数据"""
        cells = row.find_all(['td', 'th'])
        if len(cells) < 3:
            return None
        
        cell_texts = [cell.get_text().strip() for cell in cells]
        data = {}
        
        # 解析期号
        issue = self._extract_issue(cell_texts)
        if not issue:
            return None
        data['issue'] = issue
        
        # 解析日期
        date = self._extract_date(cell_texts)
        if date:
            data['draw_date'] = date
        
        # 解析开奖号码
        numbers = self._extract_numbers(cell_texts)
        if numbers and len(numbers) == 3:
            data['hundreds'] = numbers[0]
            data['tens'] = numbers[1]
            data['units'] = numbers[2]
        else:
            return None
        
        # 解析试机号码
        trial_numbers = self._extract_trial_numbers(cell_texts)
        if trial_numbers and len(trial_numbers) == 3:
            data['trial_hundreds'] = trial_numbers[0]
            data['trial_tens'] = trial_numbers[1]
            data['trial_units'] = trial_numbers[2]
        
        # 解析机器号
        machine = self._extract_machine_number(cell_texts)
        if machine:
            data['machine_number'] = machine
        
        # 解析销售额
        sales = self._extract_sales_amount(cell_texts)
        if sales:
            data['sales_amount'] = sales
        
        # 解析奖金信息
        prize = self._extract_prize_info(cell_texts)
        if prize:
            data['prize_info'] = prize
        
        return data
    
    def _extract_issue(self, cell_texts: List[str]) -> Optional[str]:
        """提取期号"""
        for text in cell_texts:
            match = re.search(self.field_patterns['issue'], text)
            if match:
                return match.group(1)
        return None
    
    def _extract_date(self, cell_texts: List[str]) -> Optional[str]:
        """提取日期"""
        for text in cell_texts:
            for date_format in self.date_formats:
                match = re.search(date_format, text)
                if match:
                    return self._normalize_date(match.group(0))
        return None
    
    def _normalize_date(self, date_str: str) -> str:
        """标准化日期格式"""
        # 将各种日期格式转换为 YYYY-MM-DD
        date_str = date_str.replace('/', '-').replace('.', '-')
        
        if len(date_str) == 8 and date_str.isdigit():
            # YYYYMMDD 格式
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        elif len(date_str) == 8 and '-' in date_str:
            # YY-MM-DD 格式
            parts = date_str.split('-')
            if len(parts[0]) == 2:
                year = f"20{parts[0]}" if int(parts[0]) < 50 else f"19{parts[0]}"
                return f"{year}-{parts[1]}-{parts[2]}"
        
        return date_str
    
    def _extract_numbers(self, cell_texts: List[str]) -> Optional[List[int]]:
        """提取开奖号码"""
        for text in cell_texts:
            # 查找三位连续数字
            match = re.search(self.field_patterns['numbers'], text)
            if match:
                numbers = [int(match.group(1)), int(match.group(2)), int(match.group(3))]
                # 验证数字范围
                if all(0 <= num <= 9 for num in numbers):
                    return numbers
        return None
    
    def _extract_trial_numbers(self, cell_texts: List[str]) -> Optional[List[int]]:
        """提取试机号码"""
        for text in cell_texts:
            if '试机' in text or 'trial' in text.lower():
                match = re.search(r'(\d)\s*(\d)\s*(\d)', text)
                if match:
                    numbers = [int(match.group(1)), int(match.group(2)), int(match.group(3))]
                    if all(0 <= num <= 9 for num in numbers):
                        return numbers
        return None
    
    def _extract_machine_number(self, cell_texts: List[str]) -> Optional[str]:
        """提取机器号"""
        for text in cell_texts:
            match = re.search(self.field_patterns['machine'], text)
            if match:
                return match.group(1)
        return None
    
    def _extract_sales_amount(self, cell_texts: List[str]) -> Optional[float]:
        """提取销售额"""
        for text in cell_texts:
            if '销售' in text or '投注' in text:
                match = re.search(self.field_patterns['sales'], text)
                if match:
                    amount_str = match.group(1).replace(',', '')
                    try:
                        return float(amount_str)
                    except ValueError:
                        continue
        return None
    
    def _extract_prize_info(self, cell_texts: List[str]) -> Optional[str]:
        """提取奖金信息"""
        for text in cell_texts:
            if '奖' in text and ('金' in text or '池' in text):
                # 清理和格式化奖金信息
                prize_info = re.sub(r'\s+', ' ', text.strip())
                if len(prize_info) > 0:
                    return prize_info
        return None
    
    def _validate_basic_data(self, data: Dict[str, Any]) -> bool:
        """验证基础数据完整性"""
        required_fields = ['issue', 'hundreds', 'tens', 'units']
        return all(field in data for field in required_fields)
    
    def _calculate_derived_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """计算衍生字段"""
        if all(field in data for field in ['hundreds', 'tens', 'units']):
            numbers = [data['hundreds'], data['tens'], data['units']]
            
            # 计算和值
            data['sum_value'] = sum(numbers)
            
            # 计算跨度
            data['span'] = max(numbers) - min(numbers)
            
            # 判断号码类型
            data['number_type'] = self._determine_number_type(numbers)
        
        return data
    
    def _determine_number_type(self, numbers: List[int]) -> str:
        """判断号码类型"""
        unique_count = len(set(numbers))
        if unique_count == 1:
            return "豹子"
        elif unique_count == 2:
            return "对子"
        else:
            return "组六"
    
    def parse_text_content(self, text_content: str) -> List[Dict[str, Any]]:
        """解析文本内容"""
        try:
            lines = text_content.strip().split('\n')
            data_list = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    data = self._parse_text_line(line)
                    if data and self._validate_basic_data(data):
                        data = self._calculate_derived_fields(data)
                        data_list.append(data)
                except Exception as e:
                    logger.warning(f"解析文本行失败: {line} - {str(e)}")
                    continue
            
            logger.info(f"文本解析完成，获得 {len(data_list)} 条记录")
            return data_list
            
        except Exception as e:
            logger.error(f"文本内容解析失败: {str(e)}")
            return []
    
    def _parse_text_line(self, line: str) -> Optional[Dict[str, Any]]:
        """解析文本行"""
        # 支持多种分隔符
        separators = [',', '\t', '|', ';']
        parts = None
        
        for sep in separators:
            if sep in line:
                parts = line.split(sep)
                break
        
        if not parts:
            # 尝试空格分割
            parts = line.split()
        
        if len(parts) < 3:
            return None
        
        data = {}
        
        # 解析期号
        issue = self._extract_issue([parts[0]])
        if not issue:
            return None
        data['issue'] = issue
        
        # 解析日期
        if len(parts) > 1:
            date = self._extract_date([parts[1]])
            if date:
                data['draw_date'] = date
        
        # 解析开奖号码
        numbers = self._extract_numbers([parts[2] if len(parts) > 2 else ''])
        if numbers and len(numbers) == 3:
            data['hundreds'] = numbers[0]
            data['tens'] = numbers[1]
            data['units'] = numbers[2]
        else:
            return None
        
        # 解析其他字段
        for i, part in enumerate(parts[3:], 3):
            if '试机' in part:
                trial_numbers = self._extract_trial_numbers([part])
                if trial_numbers:
                    data['trial_hundreds'] = trial_numbers[0]
                    data['trial_tens'] = trial_numbers[1]
                    data['trial_units'] = trial_numbers[2]
            elif '销售' in part or part.replace(',', '').replace('.', '').isdigit():
                sales = self._extract_sales_amount([part])
                if sales:
                    data['sales_amount'] = sales
            elif '机器' in part or re.match(r'[A-Z]\d+', part):
                data['machine_number'] = part.strip()
        
        return data


if __name__ == "__main__":
    # 测试数据解析器
    print("=== 福彩3D数据解析器测试 ===")
    
    parser = LotteryDataParser()
    
    # 测试文本解析
    test_text = """2024001,2024-01-01,123,456,1000000
2024002,2024-01-02,555,123,1200000
2024003,2024-01-03,112,789,980000"""
    
    data = parser.parse_text_content(test_text)
    
    print(f"解析结果:")
    for record in data:
        print(f"期号: {record['issue']}, 号码: {record['hundreds']}{record['tens']}{record['units']}, "
              f"和值: {record['sum_value']}, 类型: {record['number_type']}")
    
    print(f"\n共解析 {len(data)} 条记录")
