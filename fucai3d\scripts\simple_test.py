#!/usr/bin/env python3
"""
简化的预测测试脚本

验证系统基本功能
"""

import os
import sys
import sqlite3
import json
from datetime import datetime
from pathlib import Path

def test_database():
    """测试数据库连接"""
    try:
        db_path = Path(__file__).parent.parent / "data" / "lottery.db"
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM lottery_data")
            count = cursor.fetchone()[0]
            
            print(f"✅ 数据库连接成功，历史数据: {count} 条")
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    project_root = Path(__file__).parent.parent
    
    required_files = [
        "src/fusion/fusion_predictor.py",
        "src/predictors/sum_predictor.py",
        "src/predictors/span_predictor.py",
        "config/fusion_config.yaml",
        "config/system_logging_config.yaml",
        "p8_fusion_cli.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            all_exist = False
    
    return all_exist

def test_imports():
    """测试模块导入"""
    try:
        sys.path.insert(0, str(Path(__file__).parent.parent))
        
        # 测试基础模块导入
        from src.data.fusion_data_access import FusionDataAccess
        print("✅ FusionDataAccess 导入成功")
        
        from src.predictors.unified_predictor_interface import UnifiedPredictorInterface
        print("✅ UnifiedPredictorInterface 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def create_test_report():
    """创建测试报告"""
    print("\n🚀 开始系统基本功能测试...")
    print("=" * 50)
    
    results = {
        'test_time': datetime.now().isoformat(),
        'tests': []
    }
    
    # 测试数据库
    print("\n1. 测试数据库连接...")
    db_ok = test_database()
    results['tests'].append({'name': '数据库连接', 'success': db_ok})
    
    # 测试文件结构
    print("\n2. 测试文件结构...")
    files_ok = test_file_structure()
    results['tests'].append({'name': '文件结构', 'success': files_ok})
    
    # 测试模块导入
    print("\n3. 测试模块导入...")
    imports_ok = test_imports()
    results['tests'].append({'name': '模块导入', 'success': imports_ok})
    
    # 计算总体结果
    total_tests = len(results['tests'])
    passed_tests = sum(1 for test in results['tests'] if test['success'])
    success_rate = passed_tests / total_tests if total_tests > 0 else 0
    
    results['summary'] = {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'success_rate': success_rate,
        'overall_status': 'passed' if success_rate >= 0.8 else 'failed'
    }
    
    # 保存报告
    reports_dir = Path(__file__).parent.parent / "reports"
    reports_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = reports_dir / f"simple_test_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 测试结果摘要:")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"成功率: {success_rate:.1%}")
    print(f"总体状态: {results['summary']['overall_status']}")
    print(f"报告保存到: {report_file}")
    
    if results['summary']['overall_status'] == 'passed':
        print("\n✅ 系统基本功能测试通过！")
        print("可以继续进行下一阶段的测试。")
    else:
        print("\n❌ 系统基本功能测试失败！")
        print("请检查错误信息并修复问题。")
    
    return results['summary']['overall_status'] == 'passed'

if __name__ == "__main__":
    success = create_test_report()
    sys.exit(0 if success else 1)
