#!/usr/bin/env python3
"""
P8智能交集融合系统集成测试

验证所有组件的协同工作和系统完整性

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import unittest
import tempfile
import shutil
import sqlite3
import json
import time
from pathlib import Path
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.fusion.fusion_predictor import FusionPredictor
from src.fusion.performance_monitor import PerformanceMonitor
from src.fusion.report_generator import ReportGenerator
from src.fusion.auto_adjustment_trigger import AutoAdjustmentTrigger
from src.data.fusion_data_access import FusionDataAccess

class TestP8Integration(unittest.TestCase):
    """P8智能交集融合系统集成测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        # 创建临时目录
        cls.temp_dir = tempfile.mkdtemp()
        cls.test_db_path = os.path.join(cls.temp_dir, "test_lottery.db")
        cls.config_path = os.path.join(cls.temp_dir, "test_config.yaml")
        
        # 创建测试数据库
        cls._create_test_database()
        
        # 创建测试配置
        cls._create_test_config()
        
        print(f"测试环境初始化完成: {cls.temp_dir}")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        # 清理临时目录
        shutil.rmtree(cls.temp_dir, ignore_errors=True)
        print("测试环境清理完成")
    
    @classmethod
    def _create_test_database(cls):
        """创建测试数据库"""
        with sqlite3.connect(cls.test_db_path) as conn:
            cursor = conn.cursor()
            
            # 创建基础表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lottery_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    issue TEXT NOT NULL,
                    hundreds INTEGER NOT NULL,
                    tens INTEGER NOT NULL,
                    units INTEGER NOT NULL,
                    sum_value INTEGER NOT NULL,
                    span_value INTEGER NOT NULL,
                    draw_date TEXT NOT NULL
                )
            ''')
            
            # 创建融合系统表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS final_predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    issue TEXT NOT NULL,
                    prediction_rank INTEGER NOT NULL,
                    hundreds INTEGER NOT NULL,
                    tens INTEGER NOT NULL,
                    units INTEGER NOT NULL,
                    sum_value INTEGER NOT NULL,
                    span_value INTEGER NOT NULL,
                    combined_probability REAL NOT NULL,
                    hundreds_prob REAL NOT NULL,
                    tens_prob REAL NOT NULL,
                    units_prob REAL NOT NULL,
                    sum_prob REAL NOT NULL,
                    span_prob REAL NOT NULL,
                    sum_consistency REAL NOT NULL,
                    span_consistency REAL NOT NULL,
                    constraint_score REAL NOT NULL,
                    diversity_score REAL NOT NULL,
                    confidence_level TEXT,
                    fusion_method TEXT,
                    ranking_strategy TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(issue, prediction_rank)
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fusion_weights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    weight_type TEXT NOT NULL,
                    predictor_name TEXT NOT NULL,
                    model_name TEXT,
                    weight_value REAL NOT NULL,
                    performance_score REAL,
                    accuracy_rate REAL,
                    confidence_score REAL,
                    is_active BOOLEAN DEFAULT TRUE,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(weight_type, predictor_name, model_name)
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prediction_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    issue TEXT NOT NULL,
                    actual_hundreds INTEGER,
                    actual_tens INTEGER,
                    actual_units INTEGER,
                    actual_sum INTEGER,
                    actual_span INTEGER,
                    predicted_rank INTEGER,
                    hit_type TEXT,
                    hundreds_accuracy BOOLEAN,
                    tens_accuracy BOOLEAN,
                    units_accuracy BOOLEAN,
                    sum_accuracy BOOLEAN,
                    span_accuracy BOOLEAN,
                    overall_score REAL,
                    probability_score REAL,
                    constraint_score REAL,
                    diversity_effectiveness REAL,
                    fusion_method TEXT,
                    ranking_strategy TEXT,
                    top_k_hit INTEGER,
                    confidence_accuracy REAL,
                    evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(issue)
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fusion_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    issue TEXT NOT NULL,
                    fusion_method TEXT NOT NULL,
                    ranking_strategy TEXT NOT NULL,
                    input_data TEXT NOT NULL,
                    output_data TEXT NOT NULL,
                    execution_time REAL,
                    success BOOLEAN DEFAULT TRUE,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(session_id)
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS weight_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    weights TEXT NOT NULL,
                    performance_summary TEXT,
                    adjustment_reason TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 插入测试数据
            test_data = [
                ('2024001', 1, 2, 3, 6, 2, '2024-01-01'),
                ('2024002', 4, 5, 6, 15, 2, '2024-01-02'),
                ('2024003', 7, 8, 9, 24, 2, '2024-01-03'),
                ('2024004', 0, 1, 2, 3, 2, '2024-01-04'),
                ('2024005', 3, 4, 5, 12, 2, '2024-01-05')
            ]
            
            cursor.executemany('''
                INSERT INTO lottery_data (issue, hundreds, tens, units, sum_value, span_value, draw_date)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', test_data)
            
            # 插入默认权重
            default_weights = [
                ('position', 'hundreds', 'ensemble', 1.0, 0.7, 0.6, 0.8),
                ('position', 'tens', 'ensemble', 1.0, 0.7, 0.6, 0.8),
                ('position', 'units', 'ensemble', 1.0, 0.7, 0.6, 0.8),
                ('auxiliary', 'sum', 'ensemble', 0.8, 0.6, 0.5, 0.7),
                ('auxiliary', 'span', 'ensemble', 0.6, 0.5, 0.4, 0.6)
            ]
            
            cursor.executemany('''
                INSERT INTO fusion_weights (weight_type, predictor_name, model_name, weight_value, performance_score, accuracy_rate, confidence_score)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', default_weights)
            
            conn.commit()
    
    @classmethod
    def _create_test_config(cls):
        """创建测试配置"""
        config_content = '''
fusion:
  default_method: "adaptive_fusion"
  probability_weight: 0.5
  constraint_weight: 0.3
  diversity_weight: 0.2
  min_probability: 1.0e-6
  max_recommendations: 10

ranking:
  default_strategy: "adaptive"
  top_k: 10
  min_score_threshold: 0.01
  diversity_penalty: 0.1

weights:
  learning_rate: 0.1
  decay_factor: 0.95
  min_weight: 0.1
  max_weight: 2.0
  evaluation_window: 7
  min_samples: 3

constraints:
  sum_tolerance: 2.0
  span_tolerance: 1.0
  consistency_weight: 0.3
  diversity_weight: 0.2

database:
  path: "{}"
  timeout: 30

logging:
  level: "INFO"
  file: "logs/test_fusion.log"
'''.format(cls.test_db_path)
        
        with open(cls.config_path, 'w', encoding='utf-8') as f:
            f.write(config_content)
    
    def setUp(self):
        """每个测试方法的初始化"""
        self.fusion_predictor = None
        self.performance_monitor = None
        self.report_generator = None
        self.auto_trigger = None
    
    def tearDown(self):
        """每个测试方法的清理"""
        if self.performance_monitor and self.performance_monitor.is_monitoring:
            self.performance_monitor.stop_monitoring()
        
        if self.auto_trigger and self.auto_trigger.is_active:
            self.auto_trigger.stop_monitoring()
    
    def test_01_fusion_predictor_initialization(self):
        """测试融合预测器初始化"""
        print("\n=== 测试融合预测器初始化 ===")
        
        try:
            self.fusion_predictor = FusionPredictor(
                db_path=self.test_db_path,
                config_path=self.config_path
            )
            
            # 验证初始化状态
            self.assertTrue(self.fusion_predictor.is_initialized)
            self.assertIsNotNone(self.fusion_predictor.config)
            self.assertIsNotNone(self.fusion_predictor.unified_interface)
            self.assertIsNotNone(self.fusion_predictor.fusion_engine)
            self.assertIsNotNone(self.fusion_predictor.constraint_optimizer)
            self.assertIsNotNone(self.fusion_predictor.intelligent_ranker)
            self.assertIsNotNone(self.fusion_predictor.weight_adjuster)
            self.assertIsNotNone(self.fusion_predictor.data_access)
            
            print("✓ 融合预测器初始化成功")
            
        except Exception as e:
            self.fail(f"融合预测器初始化失败: {e}")
    
    def test_02_data_access_functionality(self):
        """测试数据访问功能"""
        print("\n=== 测试数据访问功能 ===")
        
        try:
            data_access = FusionDataAccess(self.test_db_path)
            
            # 测试获取权重
            weights = data_access.get_fusion_weights()
            self.assertIsInstance(weights, dict)
            self.assertGreater(len(weights), 0)
            print(f"✓ 获取权重成功: {len(weights)}个权重")
            
            # 测试保存预测结果
            test_predictions = [
                {
                    'rank': 1,
                    'hundreds': 1,
                    'tens': 2,
                    'units': 3,
                    'sum_value': 6,
                    'span_value': 2,
                    'combined_probability': 0.1,
                    'hundreds_prob': 0.1,
                    'tens_prob': 0.1,
                    'units_prob': 0.1,
                    'sum_prob': 0.1,
                    'span_prob': 0.1,
                    'sum_consistency': 0.8,
                    'span_consistency': 0.8,
                    'constraint_score': 0.8,
                    'diversity_score': 0.7,
                    'confidence_level': 'medium',
                    'fusion_method': 'test_method',
                    'ranking_strategy': 'test_strategy'
                }
            ]
            
            success = data_access.save_final_predictions(test_predictions, '2024006')
            self.assertTrue(success)
            print("✓ 保存预测结果成功")
            
            # 测试获取预测结果
            predictions = data_access.get_final_predictions('2024006')
            self.assertEqual(len(predictions), 1)
            self.assertEqual(predictions[0].hundreds, 1)
            print("✓ 获取预测结果成功")
            
        except Exception as e:
            self.fail(f"数据访问功能测试失败: {e}")
    
    def test_03_performance_monitoring(self):
        """测试性能监控"""
        print("\n=== 测试性能监控 ===")
        
        try:
            monitor_config = {
                'alert_rules': [],
                'monitoring_interval': 1
            }
            
            self.performance_monitor = PerformanceMonitor(
                self.test_db_path, 
                monitor_config
            )
            
            # 测试监控启动
            self.performance_monitor.start_monitoring(interval=1)
            self.assertTrue(self.performance_monitor.is_monitoring)
            print("✓ 性能监控启动成功")
            
            # 记录一些测试数据
            self.performance_monitor.record_prediction(1.5, True)
            self.performance_monitor.record_prediction(2.0, True)
            self.performance_monitor.record_prediction(1.8, False)
            
            # 等待一个监控周期
            time.sleep(2)
            
            # 获取监控摘要
            summary = self.performance_monitor.get_monitoring_summary()
            self.assertIsInstance(summary, dict)
            self.assertTrue(summary['monitoring_status'])
            self.assertEqual(summary['stats']['total_predictions'], 3)
            print("✓ 性能监控数据记录成功")
            
            # 停止监控
            self.performance_monitor.stop_monitoring()
            self.assertFalse(self.performance_monitor.is_monitoring)
            print("✓ 性能监控停止成功")
            
        except Exception as e:
            self.fail(f"性能监控测试失败: {e}")
    
    def test_04_report_generation(self):
        """测试报告生成"""
        print("\n=== 测试报告生成 ===")
        
        try:
            report_dir = os.path.join(self.temp_dir, "reports")
            self.report_generator = ReportGenerator(
                self.test_db_path,
                report_dir
            )
            
            # 生成报告
            result = self.report_generator.generate_performance_report(
                days=7,
                include_charts=False  # 跳过图表生成以避免依赖问题
            )
            
            self.assertIsInstance(result, dict)
            self.assertIn('report_data', result)
            self.assertIn('html_file', result)
            self.assertIn('json_file', result)
            
            # 验证文件存在
            self.assertTrue(Path(result['html_file']).exists())
            self.assertTrue(Path(result['json_file']).exists())
            
            print(f"✓ 报告生成成功: {result['html_file']}")
            
        except Exception as e:
            self.fail(f"报告生成测试失败: {e}")
    
    def test_05_auto_adjustment_trigger(self):
        """测试自动调整触发器"""
        print("\n=== 测试自动调整触发器 ===")
        
        try:
            # 创建一个简化的融合预测器用于测试
            if not self.fusion_predictor:
                self.fusion_predictor = FusionPredictor(
                    db_path=self.test_db_path,
                    config_path=self.config_path
                )
            
            trigger_config = {
                'cooldown_period': 10,  # 10秒冷却期
                'trigger_conditions': []
            }
            
            self.auto_trigger = AutoAdjustmentTrigger(
                self.test_db_path,
                self.fusion_predictor,
                trigger_config
            )
            
            # 测试触发器状态
            status = self.auto_trigger.get_trigger_status()
            self.assertIsInstance(status, dict)
            self.assertFalse(status['is_active'])
            print("✓ 自动调整触发器初始化成功")
            
            # 测试启动监控
            self.auto_trigger.start_monitoring(interval=1)
            self.assertTrue(self.auto_trigger.is_active)
            print("✓ 自动调整触发器启动成功")
            
            # 等待一个监控周期
            time.sleep(2)
            
            # 停止监控
            self.auto_trigger.stop_monitoring()
            self.assertFalse(self.auto_trigger.is_active)
            print("✓ 自动调整触发器停止成功")
            
        except Exception as e:
            self.fail(f"自动调整触发器测试失败: {e}")
    
    def test_06_end_to_end_prediction(self):
        """测试端到端预测流程"""
        print("\n=== 测试端到端预测流程 ===")
        
        try:
            if not self.fusion_predictor:
                self.fusion_predictor = FusionPredictor(
                    db_path=self.test_db_path,
                    config_path=self.config_path
                )
            
            # 模拟预测输入验证
            validation_result = self.fusion_predictor.validate_prediction_input('2024007')
            print(f"输入验证结果: {validation_result}")
            
            # 由于没有实际的预测器模型，我们模拟一个简化的预测流程
            # 这里主要测试系统的集成性而不是预测准确性
            
            # 测试系统状态
            status = self.fusion_predictor.get_system_status()
            self.assertIsInstance(status, dict)
            self.assertTrue(status['system_initialized'])
            print("✓ 系统状态检查通过")
            
            # 测试融合摘要
            summary = self.fusion_predictor.get_fusion_summary(days=7)
            self.assertIsInstance(summary, dict)
            print("✓ 融合摘要生成成功")
            
            # 测试预测历史
            history = self.fusion_predictor.get_prediction_history(limit=5)
            self.assertIsInstance(history, list)
            print("✓ 预测历史获取成功")
            
        except Exception as e:
            self.fail(f"端到端预测流程测试失败: {e}")
    
    def test_07_error_handling(self):
        """测试错误处理"""
        print("\n=== 测试错误处理 ===")
        
        try:
            # 测试无效数据库路径
            try:
                invalid_predictor = FusionPredictor("/invalid/path/db.db")
                # 如果没有抛出异常，说明错误处理有问题
            except Exception:
                print("✓ 无效数据库路径错误处理正确")
            
            # 测试无效配置路径
            try:
                invalid_config_predictor = FusionPredictor(
                    self.test_db_path,
                    "/invalid/config.yaml"
                )
                # 应该使用默认配置
                self.assertIsNotNone(invalid_config_predictor.config)
                print("✓ 无效配置路径错误处理正确")
            except Exception as e:
                print(f"配置错误处理测试: {e}")
            
            # 测试数据访问错误处理
            data_access = FusionDataAccess("/invalid/path/db.db")
            weights = data_access.get_fusion_weights()
            self.assertIsInstance(weights, dict)
            print("✓ 数据访问错误处理正确")
            
        except Exception as e:
            self.fail(f"错误处理测试失败: {e}")
    
    def test_08_performance_evaluation(self):
        """测试性能评估"""
        print("\n=== 测试性能评估 ===")
        
        try:
            if not self.fusion_predictor:
                self.fusion_predictor = FusionPredictor(
                    db_path=self.test_db_path,
                    config_path=self.config_path
                )
            
            # 模拟评估数据
            test_predictions = [
                {
                    'hundreds': 1, 'tens': 2, 'units': 3,
                    'sum_value': 6, 'span_value': 2,
                    'combined_probability': 0.1,
                    'constraint_score': 0.8,
                    'fusion_method': 'test_method',
                    'ranking_strategy': 'test_strategy'
                }
            ]
            
            # 测试评估和权重更新
            result = self.fusion_predictor.evaluate_and_update_weights('123', '2024007')
            
            # 由于没有实际预测数据，可能会返回错误，但不应该崩溃
            self.assertIsInstance(result, dict)
            print("✓ 性能评估流程完成")
            
            # 测试参数优化
            optimization_result = self.fusion_predictor.optimize_fusion_parameters()
            self.assertIsInstance(optimization_result, dict)
            print("✓ 参数优化流程完成")
            
        except Exception as e:
            self.fail(f"性能评估测试失败: {e}")

def run_integration_tests():
    """运行集成测试"""
    print("开始P8智能交集融合系统集成测试...")
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestP8Integration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print(f"\n{'='*50}")
    print(f"测试完成!")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun
    print(f"\n成功率: {success_rate:.1%}")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_integration_tests()
    exit(0 if success else 1)
