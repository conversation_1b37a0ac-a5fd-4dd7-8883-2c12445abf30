#!/usr/bin/env python3
"""
数据格式标准化模块

定义和实现所有预测器的统一输出格式
包括概率分布、置信度和约束信息的标准化

Author: Augment Code AI Assistant  
Date: 2025-01-14
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime
import json

@dataclass
class StandardPredictionResult:
    """标准化预测结果数据类"""
    predictor_name: str
    prediction_type: str  # 'position', 'sum', 'span'
    predicted_value: Union[int, float]
    probabilities: List[float]
    confidence: float
    constraint_info: Dict[str, Any]
    metadata: Dict[str, Any]
    timestamp: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'predictor_name': self.predictor_name,
            'prediction_type': self.prediction_type,
            'predicted_value': self.predicted_value,
            'probabilities': self.probabilities,
            'confidence': self.confidence,
            'constraint_info': self.constraint_info,
            'metadata': self.metadata,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StandardPredictionResult':
        """从字典创建实例"""
        return cls(**data)

class DataFormatStandardizer:
    """数据格式标准化器"""
    
    def __init__(self):
        """初始化标准化器"""
        self.format_specs = {
            'position': {
                'output_size': 10,
                'value_range': (0, 9),
                'probability_sum': 1.0,
                'required_fields': ['predicted_digit', 'probabilities', 'confidence']
            },
            'sum': {
                'output_size': 28,
                'value_range': (0, 27),
                'probability_sum': 1.0,
                'required_fields': ['predicted_sum', 'probabilities', 'confidence']
            },
            'span': {
                'output_size': 10,
                'value_range': (0, 9),
                'probability_sum': 1.0,
                'required_fields': ['predicted_span', 'probabilities', 'confidence']
            }
        }
    
    def standardize_position_prediction(self, raw_result: Dict[str, Any], 
                                      predictor_name: str) -> StandardPredictionResult:
        """
        标准化位置预测结果
        
        Args:
            raw_result: 原始预测结果
            predictor_name: 预测器名称
            
        Returns:
            标准化预测结果
        """
        try:
            # 提取核心数据
            predicted_digit = raw_result.get('predicted_digit', 0)
            probabilities = raw_result.get('probabilities', [0.1] * 10)
            confidence = raw_result.get('confidence', 0.5)
            
            # 验证和修正概率分布
            probabilities = self._normalize_probabilities(probabilities, 10)
            
            # 构建约束信息
            constraint_info = {
                'value_range': (0, 9),
                'probability_distribution': probabilities,
                'top_k_predictions': self._get_top_k_predictions(probabilities, k=3),
                'entropy': self._calculate_entropy(probabilities)
            }
            
            # 构建元数据
            metadata = {
                'output_size': 10,
                'prediction_method': raw_result.get('model_name', 'unknown'),
                'feature_count': raw_result.get('feature_count', 0),
                'training_samples': raw_result.get('training_samples', 0)
            }
            
            return StandardPredictionResult(
                predictor_name=predictor_name,
                prediction_type='position',
                predicted_value=int(predicted_digit),
                probabilities=probabilities,
                confidence=float(confidence),
                constraint_info=constraint_info,
                metadata=metadata,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            return self._create_error_result(predictor_name, 'position', str(e))
    
    def standardize_sum_prediction(self, raw_result: Dict[str, Any], 
                                 predictor_name: str) -> StandardPredictionResult:
        """
        标准化和值预测结果
        
        Args:
            raw_result: 原始预测结果
            predictor_name: 预测器名称
            
        Returns:
            标准化预测结果
        """
        try:
            # 提取核心数据
            predicted_sum = raw_result.get('predicted_sum', 13.5)
            probabilities = raw_result.get('probabilities', [1/28] * 28)
            confidence = raw_result.get('confidence', 0.5)
            
            # 验证和修正概率分布
            probabilities = self._normalize_probabilities(probabilities, 28)
            
            # 构建约束信息
            constraint_info = {
                'value_range': (0, 27),
                'probability_distribution': probabilities,
                'top_k_predictions': self._get_top_k_predictions(probabilities, k=5),
                'entropy': self._calculate_entropy(probabilities),
                'constraint_tolerance': raw_result.get('constraint_info', {}).get('tolerance', 2.0)
            }
            
            # 添加原始约束信息
            if 'constraint_info' in raw_result:
                constraint_info.update(raw_result['constraint_info'])
            
            # 构建元数据
            metadata = {
                'output_size': 28,
                'prediction_method': raw_result.get('model_name', 'unknown'),
                'feature_count': raw_result.get('feature_count', 0),
                'training_samples': raw_result.get('training_samples', 0),
                'sum_range': (0, 27)
            }
            
            return StandardPredictionResult(
                predictor_name=predictor_name,
                prediction_type='sum',
                predicted_value=float(predicted_sum),
                probabilities=probabilities,
                confidence=float(confidence),
                constraint_info=constraint_info,
                metadata=metadata,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            return self._create_error_result(predictor_name, 'sum', str(e))
    
    def standardize_span_prediction(self, raw_result: Dict[str, Any], 
                                   predictor_name: str) -> StandardPredictionResult:
        """
        标准化跨度预测结果
        
        Args:
            raw_result: 原始预测结果
            predictor_name: 预测器名称
            
        Returns:
            标准化预测结果
        """
        try:
            # 提取核心数据
            predicted_span = raw_result.get('predicted_span', 5)
            probabilities = raw_result.get('probabilities', [0.1] * 10)
            confidence = raw_result.get('confidence', 0.5)
            
            # 验证和修正概率分布
            probabilities = self._normalize_probabilities(probabilities, 10)
            
            # 构建约束信息
            constraint_info = {
                'value_range': (0, 9),
                'probability_distribution': probabilities,
                'top_k_predictions': self._get_top_k_predictions(probabilities, k=3),
                'entropy': self._calculate_entropy(probabilities),
                'constraint_tolerance': raw_result.get('constraint_info', {}).get('tolerance', 1.0)
            }
            
            # 添加原始约束信息
            if 'constraint_info' in raw_result:
                constraint_info.update(raw_result['constraint_info'])
            
            # 构建元数据
            metadata = {
                'output_size': 10,
                'prediction_method': raw_result.get('model_name', 'unknown'),
                'feature_count': raw_result.get('feature_count', 0),
                'training_samples': raw_result.get('training_samples', 0),
                'span_range': (0, 9)
            }
            
            return StandardPredictionResult(
                predictor_name=predictor_name,
                prediction_type='span',
                predicted_value=int(predicted_span),
                probabilities=probabilities,
                confidence=float(confidence),
                constraint_info=constraint_info,
                metadata=metadata,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            return self._create_error_result(predictor_name, 'span', str(e))
    
    def _normalize_probabilities(self, probabilities: List[float], expected_size: int) -> List[float]:
        """归一化概率分布"""
        try:
            # 确保长度正确
            if len(probabilities) != expected_size:
                probabilities = probabilities[:expected_size] + [0.0] * max(0, expected_size - len(probabilities))
            
            # 转换为numpy数组进行计算
            probs = np.array(probabilities, dtype=float)
            
            # 处理负值和NaN
            probs = np.where(np.isnan(probs), 0.0, probs)
            probs = np.where(probs < 0, 0.0, probs)
            
            # 归一化
            total = np.sum(probs)
            if total > 0:
                probs = probs / total
            else:
                probs = np.ones(expected_size) / expected_size
            
            return probs.tolist()
            
        except Exception:
            # 如果出错，返回均匀分布
            return [1.0 / expected_size] * expected_size
    
    def _get_top_k_predictions(self, probabilities: List[float], k: int = 3) -> List[Dict[str, Any]]:
        """获取Top-K预测"""
        try:
            indexed_probs = [(i, prob) for i, prob in enumerate(probabilities)]
            indexed_probs.sort(key=lambda x: x[1], reverse=True)
            
            top_k = []
            for i in range(min(k, len(indexed_probs))):
                idx, prob = indexed_probs[i]
                top_k.append({
                    'value': idx,
                    'probability': prob,
                    'rank': i + 1
                })
            
            return top_k
            
        except Exception:
            return []
    
    def _calculate_entropy(self, probabilities: List[float]) -> float:
        """计算概率分布的熵"""
        try:
            probs = np.array(probabilities)
            probs = probs[probs > 0]  # 只考虑非零概率
            if len(probs) == 0:
                return 0.0
            return float(-np.sum(probs * np.log2(probs)))
        except Exception:
            return 0.0
    
    def _create_error_result(self, predictor_name: str, prediction_type: str, error_msg: str) -> StandardPredictionResult:
        """创建错误结果"""
        spec = self.format_specs.get(prediction_type, {'output_size': 10})
        output_size = spec['output_size']
        
        return StandardPredictionResult(
            predictor_name=predictor_name,
            prediction_type=prediction_type,
            predicted_value=0,
            probabilities=[1.0 / output_size] * output_size,
            confidence=0.0,
            constraint_info={'error': error_msg},
            metadata={'error': error_msg},
            timestamp=datetime.now().isoformat()
        )
    
    def validate_standard_result(self, result: StandardPredictionResult) -> Dict[str, bool]:
        """验证标准化结果"""
        validation = {
            'predictor_name_valid': bool(result.predictor_name),
            'prediction_type_valid': result.prediction_type in self.format_specs,
            'probabilities_valid': False,
            'confidence_valid': 0.0 <= result.confidence <= 1.0,
            'value_range_valid': False
        }
        
        if result.prediction_type in self.format_specs:
            spec = self.format_specs[result.prediction_type]
            
            # 验证概率分布
            validation['probabilities_valid'] = (
                len(result.probabilities) == spec['output_size'] and
                abs(sum(result.probabilities) - 1.0) < 1e-6
            )
            
            # 验证值范围
            min_val, max_val = spec['value_range']
            validation['value_range_valid'] = min_val <= result.predicted_value <= max_val
        
        return validation
