#!/usr/bin/env python3
"""
P9智能优化管理器

该模块提供P9闭环优化系统的高级管理功能，包括：
1. 系统启动、停止和状态管理
2. 预检查和健康度评估
3. 手动触发优化任务
4. P8系统集成状态监控

作者: Augment Code AI Assistant
创建日期: 2025-01-14
版本: 1.0.0
"""

import os
import sys
import sqlite3
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import threading
import time

# 尝试导入可选依赖
try:
    import psutil
except ImportError:
    psutil = None

try:
    import shutil
except ImportError:
    shutil = None

from .intelligent_closed_loop_optimizer import IntelligentClosedLoopOptimizer, OptimizationTaskType

class IntelligentOptimizationManager:
    """智能优化管理器 - P9高级管理组件"""
    
    def __init__(self, db_path: str, config_path: str = "config/"):
        """
        初始化智能优化管理器
        
        Args:
            db_path: 数据库路径
            config_path: 配置文件路径
        """
        self.db_path = db_path
        self.config_path = Path(config_path)
        self.logger = logging.getLogger(__name__)
        
        # 核心组件
        self.optimizer = None
        
        # 任务优先级管理
        self.task_priorities = {
            OptimizationTaskType.DATA_UPDATE: 8,
            OptimizationTaskType.PERFORMANCE_EVAL: 7,
            OptimizationTaskType.FUSION_WEIGHT_ADJUST: 6,
            OptimizationTaskType.PARAMETER_TUNE: 5,
            OptimizationTaskType.MODEL_RETRAIN: 4,
            OptimizationTaskType.SYSTEM_MAINTENANCE: 3
        }
        
        # 系统状态缓存
        self._status_cache = {}
        self._cache_expiry = None
        
        # 初始化组件
        self._initialize_components()
        
        self.logger.info("智能优化管理器初始化完成")
    
    def _initialize_components(self):
        """初始化核心组件"""
        try:
            # 初始化优化控制器
            self.optimizer = IntelligentClosedLoopOptimizer(self.db_path, str(self.config_path))
            
            self.logger.info("核心组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"核心组件初始化失败: {e}")
            raise
    
    def start_system(self) -> Dict[str, Any]:
        """启动智能闭环系统"""
        try:
            # 预检查系统状态
            pre_check_result = self._pre_start_check()
            if not pre_check_result['success']:
                return {
                    'status': 'failed',
                    'error': f"预检查失败: {pre_check_result['error']}",
                    'pre_check_details': pre_check_result
                }
            
            # 启动优化器
            self.optimizer.start_optimization_loop()
            
            # 记录启动事件
            self._log_system_event('system_start', 'manual', {'pre_check': pre_check_result})
            
            self.logger.info("智能闭环优化系统启动成功")
            
            return {
                'status': 'success',
                'message': '智能闭环优化系统已启动',
                'system_info': self._get_system_info(),
                'pre_check_result': pre_check_result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"智能闭环系统启动失败: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    def stop_system(self) -> Dict[str, Any]:
        """停止智能闭环系统"""
        try:
            # 获取停止前状态
            final_status = self.get_system_status()
            
            # 停止优化器
            self.optimizer.stop_optimization_loop()
            
            # 记录停止事件
            self._log_system_event('system_stop', 'manual', {'final_status': final_status})
            
            self.logger.info("智能闭环优化系统停止成功")
            
            return {
                'status': 'success',
                'message': '智能闭环优化系统已停止',
                'final_status': final_status,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"智能闭环系统停止失败: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态 (增强版)"""
        try:
            # 检查缓存
            if self._is_cache_valid():
                return self._status_cache
            
            # 基础状态
            status = {
                'is_running': self.optimizer.is_running if self.optimizer else False,
                'optimization_config': self.optimizer.optimization_config if self.optimizer else {},
                'performance_thresholds': self.optimizer.performance_thresholds if self.optimizer else {},
                'timestamp': datetime.now().isoformat()
            }
            
            # P8系统状态
            status['p8_system_status'] = self._get_p8_system_status()
            
            # 性能数据
            if self.optimizer:
                status['recent_performance'] = self.optimizer.get_recent_performance()
            
            # 优化历史
            status['last_optimization'] = self.get_last_optimization_info()
            status['optimization_summary'] = self._get_optimization_summary()
            
            # 系统健康度评估
            status['system_health'] = self._assess_system_health(status)
            
            # 更新缓存
            self._status_cache = status
            self._cache_expiry = datetime.now() + timedelta(seconds=30)
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
    
    def _pre_start_check(self) -> Dict[str, Any]:
        """启动前预检查"""
        checks = {
            'database_connection': False,
            'p8_components': False,
            'configuration': False,
            'disk_space': False,
            'memory_available': False
        }
        
        errors = []
        
        try:
            # 数据库连接检查
            conn = sqlite3.connect(self.db_path)
            conn.execute("SELECT 1")
            conn.close()
            checks['database_connection'] = True
        except Exception as e:
            errors.append(f"数据库连接失败: {e}")
        
        try:
            # P8组件检查
            if self.optimizer:
                p8_status = self._get_p8_system_status()
                if p8_status.get('components_available', 0) > 0:
                    checks['p8_components'] = True
                else:
                    errors.append("P8系统组件未完全初始化")
            else:
                errors.append("优化器未初始化")
        except Exception as e:
            errors.append(f"P8组件检查失败: {e}")
        
        try:
            # 配置检查
            if (self.optimizer and 
                self.optimizer.optimization_config and 
                self.optimizer.performance_thresholds):
                checks['configuration'] = True
            else:
                errors.append("优化配置不完整")
        except Exception as e:
            errors.append(f"配置检查失败: {e}")
        
        try:
            # 磁盘空间检查
            if shutil:
                disk_usage = shutil.disk_usage(Path(self.db_path).parent)
                free_gb = disk_usage.free / (1024**3)
                if free_gb > 1.0:  # 至少1GB可用空间
                    checks['disk_space'] = True
                else:
                    errors.append(f"磁盘空间不足: {free_gb:.2f}GB")
            else:
                checks['disk_space'] = True  # 无法检查时默认通过
                errors.append("shutil模块不可用，跳过磁盘空间检查")
        except Exception as e:
            errors.append(f"磁盘空间检查失败: {e}")
        
        try:
            # 内存检查
            if psutil:
                memory = psutil.virtual_memory()
                if memory.percent < 90:  # 内存使用率低于90%
                    checks['memory_available'] = True
                else:
                    errors.append(f"内存使用率过高: {memory.percent:.1f}%")
            else:
                checks['memory_available'] = True  # 无法检查时默认通过
                errors.append("psutil模块不可用，跳过内存检查")
        except Exception as e:
            errors.append(f"内存检查失败: {e}")
        
        success = all(checks.values())
        
        return {
            'success': success,
            'checks': checks,
            'errors': errors,
            'timestamp': datetime.now().isoformat()
        }
    
    def _get_p8_system_status(self) -> Dict[str, Any]:
        """获取P8系统状态"""
        try:
            if not self.optimizer:
                return {'status': 'optimizer_not_initialized', 'components_available': 0}
            
            components_status = {
                'fusion_predictor': {
                    'available': self.optimizer.fusion_predictor is not None,
                    'status': 'available' if self.optimizer.fusion_predictor else 'unavailable'
                },
                'performance_monitor': {
                    'available': self.optimizer.performance_monitor is not None,
                    'status': 'available' if self.optimizer.performance_monitor else 'unavailable'
                },
                'weight_adjuster': {
                    'available': self.optimizer.weight_adjuster is not None,
                    'status': 'available' if self.optimizer.weight_adjuster else 'unavailable'
                },
                'unified_interface': {
                    'available': self.optimizer.unified_interface is not None,
                    'status': 'available' if self.optimizer.unified_interface else 'unavailable'
                }
            }
            
            # 统计可用组件数量
            available_count = sum(1 for comp in components_status.values() if comp['available'])
            total_count = len(components_status)
            
            # 确定整体状态
            if available_count == total_count:
                overall_status = 'fully_integrated'
            elif available_count > total_count // 2:
                overall_status = 'partially_integrated'
            elif available_count > 0:
                overall_status = 'minimal_integration'
            else:
                overall_status = 'mock_mode'
            
            return {
                'overall_status': overall_status,
                'components_available': available_count,
                'components_total': total_count,
                'integration_percentage': (available_count / total_count) * 100,
                'components': components_status
            }
            
        except Exception as e:
            self.logger.error(f"获取P8系统状态失败: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            info = {
                'python_version': sys.version,
                'platform': sys.platform,
                'database_size_mb': os.path.getsize(self.db_path) / (1024**2) if os.path.exists(self.db_path) else 0,
                'config_path': str(self.config_path),
                'db_path': self.db_path
            }
            
            # 可选的系统信息
            if psutil:
                info.update({
                    'cpu_count': psutil.cpu_count(),
                    'memory_total_gb': psutil.virtual_memory().total / (1024**3)
                })
            
            if shutil:
                info['disk_free_gb'] = shutil.disk_usage(Path(self.db_path).parent).free / (1024**3)
            
            return info
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {'error': str(e)}
    
    def _assess_system_health(self, status: Dict[str, Any]) -> Dict[str, Any]:
        """评估系统健康度"""
        try:
            health_score = 0.0
            
            # P8集成健康度 (30分)
            p8_status = status.get('p8_system_status', {})
            integration_percentage = p8_status.get('integration_percentage', 0)
            health_score += (integration_percentage / 100) * 30
            
            # 系统运行状态 (20分)
            if status.get('is_running', False):
                health_score += 20
            
            # 配置完整性 (25分)
            if status.get('optimization_config') and status.get('performance_thresholds'):
                health_score += 25
            
            # 数据库状态 (25分)
            if os.path.exists(self.db_path):
                health_score += 25
            
            # 标准化分数
            health_percentage = min(100.0, health_score)
            
            # 确定健康等级
            if health_percentage >= 90:
                health_level = 'excellent'
            elif health_percentage >= 80:
                health_level = 'good'
            elif health_percentage >= 60:
                health_level = 'fair'
            elif health_percentage >= 40:
                health_level = 'poor'
            else:
                health_level = 'critical'
            
            return {
                'overall_score': health_percentage,
                'health_level': health_level,
                'components': {
                    'p8_integration': (integration_percentage / 100) * 30,
                    'system_running': 20 if status.get('is_running', False) else 0,
                    'configuration': 25 if status.get('optimization_config') else 0,
                    'database': 25 if os.path.exists(self.db_path) else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"评估系统健康度失败: {e}")
            return {'overall_score': 0.0, 'health_level': 'unknown', 'error': str(e)}
    
    def get_last_optimization_info(self) -> Dict[str, Any]:
        """获取最后一次优化信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT optimization_type, trigger_reason, component_name, start_time, 
                       end_time, status, details, improvement_score
                FROM optimization_logs
                ORDER BY start_time DESC
                LIMIT 1
            """
            
            df = pd.read_sql_query(query, conn)
            
            if not df.empty:
                result = df.iloc[0].to_dict()
                if result['details']:
                    try:
                        result['details'] = json.loads(result['details'])
                    except:
                        pass
            else:
                result = {'message': '暂无优化记录'}
            
            conn.close()
            return result
            
        except Exception as e:
            self.logger.error(f"获取最后优化信息失败: {e}")
            return {'error': str(e)}
    
    def _get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化摘要"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 获取最近24小时的优化统计
            query = """
                SELECT 
                    COUNT(*) as total_optimizations,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                    AVG(improvement_score) as avg_improvement
                FROM optimization_logs
                WHERE start_time > datetime('now', '-24 hours')
            """
            
            result = conn.execute(query).fetchone()
            
            summary = {
                'total_optimizations': result[0] if result else 0,
                'successful_optimizations': result[1] if result else 0,
                'failed_optimizations': result[2] if result else 0,
                'average_improvement': result[3] if result and result[3] else 0.0,
                'success_rate': (result[1] / result[0] * 100) if result and result[0] > 0 else 0.0
            }
            
            conn.close()
            return summary
            
        except Exception as e:
            self.logger.error(f"获取优化摘要失败: {e}")
            return {'error': str(e)}
    
    def manual_trigger_optimization(self, optimization_type: str, component: str = None) -> Dict[str, Any]:
        """手动触发优化"""
        try:
            # 验证优化类型
            try:
                task_type = OptimizationTaskType(optimization_type)
            except ValueError:
                return {'status': 'failed', 'error': f'无效的优化类型: {optimization_type}'}
            
            # 触发优化
            if self.optimizer:
                self.optimizer._trigger_optimization(f"manual_{optimization_type}")
                
                self.logger.info(f"手动触发优化任务: {optimization_type}")
                
                return {
                    'status': 'success',
                    'message': f'优化任务已触发',
                    'task_type': optimization_type,
                    'component': component,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {'status': 'failed', 'error': '优化器未初始化'}
            
        except Exception as e:
            self.logger.error(f"手动触发优化失败: {e}")
            return {'status': 'failed', 'error': str(e)}
    
    def _log_system_event(self, event_type: str, trigger_reason: str, details: Dict[str, Any]):
        """记录系统事件"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO optimization_logs 
                (optimization_type, trigger_reason, start_time, status, details, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                event_type,
                trigger_reason,
                datetime.now().isoformat(),
                'completed',
                json.dumps(details),
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"记录系统事件失败: {e}")
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        return (self._cache_expiry is not None and 
                datetime.now() < self._cache_expiry and 
                bool(self._status_cache))
